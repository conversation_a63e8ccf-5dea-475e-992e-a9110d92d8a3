import { Currency } from '@/types'
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { formatCompactCurrency as formatCompactCurrencyNew, formatCurrency as formatCurrencyNew } from './currency'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency: Currency = "INR"): string {
  return formatCurrencyNew(amount, currency)
}

export function formatCompactCurrency(amount: number, currency: Currency = "INR"): string {
  return formatCompactCurrencyNew(amount, currency)
}

export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`
}

export function calculateROI(currentValue: number, initialValue: number): number {
  return ((currentValue - initialValue) / initialValue) * 100
}

export function calculateTokensFromAmount(amount: number, tokenPrice: number): number {
  return Math.floor(amount / tokenPrice)
}

export function calculateAmountFromTokens(tokens: number, tokenPrice: number): number {
  return tokens * tokenPrice
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export function formatDateTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'active':
    case 'verified':
    case 'completed':
    case 'funded':
      return 'text-green-600 bg-green-50'
    case 'pending':
    case 'review':
    case 'processing':
      return 'text-orange-600 bg-orange-50'
    case 'rejected':
    case 'failed':
    case 'cancelled':
      return 'text-red-600 bg-red-50'
    case 'draft':
    case 'inactive':
      return 'text-gray-600 bg-gray-50'
    default:
      return 'text-blue-600 bg-blue-50'
  }
}

export function getRiskColor(riskLevel: string): string {
  switch (riskLevel.toLowerCase()) {
    case 'low':
      return 'text-green-600 bg-green-50'
    case 'medium':
    case 'moderate':
      return 'text-yellow-600 bg-yellow-50'
    case 'high':
      return 'text-red-600 bg-red-50'
    default:
      return 'text-gray-600 bg-gray-50'
  }
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
