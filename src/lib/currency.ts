import { Currency, FiatCurrency, Cryptocurrency, ExchangeRate } from '@/types'

// Currency Information
export const FIAT_CURRENCIES: Record<FiatCurrency, { name: string; symbol: string; locale: string }> = {
  INR: { name: 'Indian Rupee', symbol: '₹', locale: 'en-IN' },
  USD: { name: 'US Dollar', symbol: '$', locale: 'en-US' },
  EUR: { name: 'Euro', symbol: '€', locale: 'en-EU' },
  GBP: { name: 'British Pound', symbol: '£', locale: 'en-GB' },
  AUD: { name: 'Australian Dollar', symbol: 'A$', locale: 'en-AU' },
  CAD: { name: 'Canadian Dollar', symbol: 'C$', locale: 'en-CA' },
  SGD: { name: 'Singapore Dollar', symbol: 'S$', locale: 'en-SG' },
  AED: { name: 'UAE Dirham', symbol: 'د.إ', locale: 'ar-AE' },
  JPY: { name: 'Japanese Yen', symbol: '¥', locale: 'ja-<PERSON>' }
}

export const CRYPTOCURRENCIES: Record<Cryptocurrency, { name: string; symbol: string; decimals: number }> = {
  BTC: { name: 'Bitcoin', symbol: '₿', decimals: 8 },
  ETH: { name: 'Ethereum', symbol: 'Ξ', decimals: 18 },
  USDT: { name: 'Tether USD', symbol: 'USDT', decimals: 6 },
  USDC: { name: 'USD Coin', symbol: 'USDC', decimals: 6 },
  BNB: { name: 'Binance Coin', symbol: 'BNB', decimals: 18 },
  MATIC: { name: 'Polygon', symbol: 'MATIC', decimals: 18 },
  SOL: { name: 'Solana', symbol: 'SOL', decimals: 9 },
  ADA: { name: 'Cardano', symbol: 'ADA', decimals: 6 }
}

// Mock exchange rates (in a real app, these would come from an API)
export const MOCK_EXCHANGE_RATES: ExchangeRate[] = [
  // Fiat to USD rates
  { from: 'INR', to: 'USD', rate: 0.012, lastUpdated: new Date().toISOString() },
  { from: 'EUR', to: 'USD', rate: 1.08, lastUpdated: new Date().toISOString() },
  { from: 'GBP', to: 'USD', rate: 1.27, lastUpdated: new Date().toISOString() },
  { from: 'AUD', to: 'USD', rate: 0.66, lastUpdated: new Date().toISOString() },
  { from: 'CAD', to: 'USD', rate: 0.74, lastUpdated: new Date().toISOString() },
  { from: 'SGD', to: 'USD', rate: 0.74, lastUpdated: new Date().toISOString() },
  { from: 'AED', to: 'USD', rate: 0.27, lastUpdated: new Date().toISOString() },
  { from: 'JPY', to: 'USD', rate: 0.0067, lastUpdated: new Date().toISOString() },
  
  // Crypto to USD rates
  { from: 'BTC', to: 'USD', rate: 43500, lastUpdated: new Date().toISOString() },
  { from: 'ETH', to: 'USD', rate: 2650, lastUpdated: new Date().toISOString() },
  { from: 'USDT', to: 'USD', rate: 1.0, lastUpdated: new Date().toISOString() },
  { from: 'USDC', to: 'USD', rate: 1.0, lastUpdated: new Date().toISOString() },
  { from: 'BNB', to: 'USD', rate: 315, lastUpdated: new Date().toISOString() },
  { from: 'MATIC', to: 'USD', rate: 0.85, lastUpdated: new Date().toISOString() },
  { from: 'SOL', to: 'USD', rate: 98, lastUpdated: new Date().toISOString() },
  { from: 'ADA', to: 'USD', rate: 0.48, lastUpdated: new Date().toISOString() }
]

export function isFiatCurrency(currency: Currency): currency is FiatCurrency {
  return currency in FIAT_CURRENCIES
}

export function isCryptocurrency(currency: Currency): currency is Cryptocurrency {
  return currency in CRYPTOCURRENCIES
}

export function getCurrencyInfo(currency: Currency) {
  if (isFiatCurrency(currency)) {
    return FIAT_CURRENCIES[currency]
  } else if (isCryptocurrency(currency)) {
    return CRYPTOCURRENCIES[currency]
  }
  return null
}

export function formatCurrency(amount: number, currency: Currency): string {
  const info = getCurrencyInfo(currency)
  if (!info) return amount.toString()

  if (isFiatCurrency(currency)) {
    const fiatInfo = info as typeof FIAT_CURRENCIES[FiatCurrency]
    return new Intl.NumberFormat(fiatInfo.locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount)
  } else {
    const cryptoInfo = info as typeof CRYPTOCURRENCIES[Cryptocurrency]
    const decimals = amount < 1 ? cryptoInfo.decimals : Math.min(cryptoInfo.decimals, 4)
    return `${amount.toFixed(decimals)} ${cryptoInfo.symbol}`
  }
}

export function formatCompactCurrency(amount: number, currency: Currency): string {
  const info = getCurrencyInfo(currency)
  if (!info) return amount.toString()

  const formatNumber = (num: number, suffix: string) => {
    if (isFiatCurrency(currency)) {
      const fiatInfo = info as typeof FIAT_CURRENCIES[FiatCurrency]
      return `${fiatInfo.symbol}${num.toFixed(1)}${suffix}`
    } else {
      const cryptoInfo = info as typeof CRYPTOCURRENCIES[Cryptocurrency]
      return `${num.toFixed(4)} ${cryptoInfo.symbol}${suffix}`
    }
  }

  if (isFiatCurrency(currency)) {
    if (amount >= 10000000) { // 10M
      return formatNumber(amount / 1000000, 'M')
    } else if (amount >= 100000) { // 100K
      return formatNumber(amount / 1000, 'K')
    }
  } else {
    if (amount >= 1000000) {
      return formatNumber(amount / 1000000, 'M')
    } else if (amount >= 1000) {
      return formatNumber(amount / 1000, 'K')
    }
  }
  
  return formatCurrency(amount, currency)
}

export function getExchangeRate(from: Currency, to: Currency): number {
  if (from === to) return 1

  // Find direct rate
  const directRate = MOCK_EXCHANGE_RATES.find(rate => rate.from === from && rate.to === to)
  if (directRate) return directRate.rate

  // Find inverse rate
  const inverseRate = MOCK_EXCHANGE_RATES.find(rate => rate.from === to && rate.to === from)
  if (inverseRate) return 1 / inverseRate.rate

  // Convert through USD
  const fromToUSD = MOCK_EXCHANGE_RATES.find(rate => rate.from === from && rate.to === 'USD')
  const toFromUSD = MOCK_EXCHANGE_RATES.find(rate => rate.from === 'USD' && rate.to === to)
  
  if (fromToUSD && toFromUSD) {
    return fromToUSD.rate * toFromUSD.rate
  }

  const usdToFrom = MOCK_EXCHANGE_RATES.find(rate => rate.from === 'USD' && rate.to === from)
  const usdToTo = MOCK_EXCHANGE_RATES.find(rate => rate.from === 'USD' && rate.to === to)
  
  if (usdToFrom && usdToTo) {
    return usdToTo.rate / usdToFrom.rate
  }

  // Default fallback
  return 1
}

export function convertCurrency(amount: number, from: Currency, to: Currency): number {
  const rate = getExchangeRate(from, to)
  return amount * rate
}

export function getAllSupportedCurrencies(): Currency[] {
  return [...Object.keys(FIAT_CURRENCIES), ...Object.keys(CRYPTOCURRENCIES)] as Currency[]
}

export function getFiatCurrencies(): FiatCurrency[] {
  return Object.keys(FIAT_CURRENCIES) as FiatCurrency[]
}

export function getCryptocurrencies(): Cryptocurrency[] {
  return Object.keys(CRYPTOCURRENCIES) as Cryptocurrency[]
}
