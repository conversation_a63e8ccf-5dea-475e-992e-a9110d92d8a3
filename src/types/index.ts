// Currency Types
export type FiatCurrency = 'INR' | 'USD' | 'EUR' | 'GBP' | 'AUD' | 'CAD' | 'SGD' | 'AED' | 'JPY'
export type Cryptocurrency = 'BTC' | 'ETH' | 'USDT' | 'USDC' | 'BNB' | 'MATIC' | 'SOL' | 'ADA'
export type Currency = FiatCurrency | Cryptocurrency

// Payment Method Types
export type FiatPaymentMethod = 'bank_transfer' | 'upi' | 'card' | 'wallet' | 'wire_transfer'
export type CryptoPaymentMethod = 'crypto_wallet' | 'metamask' | 'coinbase' | 'binance' | 'trust_wallet'
export type PaymentMethod = FiatPaymentMethod | CryptoPaymentMethod

// User Types
export type UserType = 'retail_investor' | 'institutional_investor' | 'developer' | 'admin'

export interface User {
  id: string
  email: string
  name: string
  userType: UserType
  avatar?: string
  kycStatus: 'pending' | 'verified' | 'rejected'
  kycProgress: number
  createdAt: string
  lastLogin: string
  isActive: boolean
  profile: UserProfile
}

export interface UserProfile {
  phone?: string
  address?: string
  city?: string
  country?: string
  investmentExperience?: 'beginner' | 'intermediate' | 'advanced'
  riskTolerance?: 'conservative' | 'moderate' | 'aggressive'
  annualIncome?: number
  netWorth?: number
  investmentGoals?: string[]
  preferredCurrency: Currency
  language?: string
  timezone?: string
}

// Property Types
export type PropertyType = 'residential' | 'commercial' | 'mixed_use'
export type PropertyStatus = 'active_funding' | 'fully_funded' | 'completed' | 'draft' | 'under_review' | 'rejected'

export interface Property {
  id: string
  title: string
  description: string
  type: PropertyType
  status: PropertyStatus
  location: PropertyLocation
  images: string[]
  documents: PropertyDocument[]
  financials: PropertyFinancials
  tokenomics: PropertyTokenomics
  developer: Developer
  metrics: PropertyMetrics
  timeline: PropertyTimeline
  createdAt: string
  updatedAt: string
}

export interface PropertyLocation {
  address: string
  city: string
  state: string
  country: string
  coordinates?: {
    lat: number
    lng: number
  }
  nearbyAmenities: string[]
}

export interface PropertyDocument {
  id: string
  name: string
  type: 'legal' | 'financial' | 'technical' | 'marketing'
  url: string
  uploadedAt: string
}

export interface PropertyFinancials {
  totalValue: number
  currency: Currency
  expectedROI: number
  rentalYield: number
  appreciationRate: number
  operatingExpenses: number
  managementFee: number
  platformFee: number
}

export interface PropertyTokenomics {
  tokenPrice: number
  totalTokens: number
  availableTokens: number
  soldTokens: number
  minimumInvestment: number
  maximumInvestment?: number
  fundingTarget: number
  fundingDeadline: string
  fundingProgress: number
}

export interface PropertyMetrics {
  size: number
  sizeUnit: 'sqft' | 'sqm'
  bedrooms?: number
  bathrooms?: number
  parking?: number
  yearBuilt?: number
  occupancyRate?: number
  tenantCount?: number
}

export interface PropertyTimeline {
  listingDate: string
  fundingStartDate: string
  fundingEndDate: string
  constructionStartDate?: string
  expectedCompletionDate?: string
  firstRentalDate?: string
}

// Developer Types
export interface Developer {
  id: string
  name: string
  logo?: string
  description: string
  website?: string
  establishedYear: number
  totalProjects: number
  completedProjects: number
  totalFunding: number
  successRate: number
  rating: number
  verificationStatus: 'verified' | 'pending' | 'rejected'
  contact: {
    email: string
    phone: string
    address: string
  }
}

// Investment Types
export type InvestmentStatus = 'pending' | 'confirmed' | 'failed' | 'cancelled'

export interface Investment {
  id: string
  userId: string
  propertyId: string
  amount: number
  tokens: number
  tokenPrice: number
  status: InvestmentStatus
  paymentMethod: PaymentMethod
  transactionId: string
  investedAt: string
  confirmedAt?: string
  currentValue: number
  totalReturns: number
  roi: number
}

// Portfolio Types
export interface Portfolio {
  userId: string
  totalInvested: number
  currentValue: number
  totalReturns: number
  roi: number
  investments: Investment[]
  distributions: Distribution[]
  summary: PortfolioSummary
}

export interface PortfolioSummary {
  totalProperties: number
  activeInvestments: number
  monthlyIncome: number
  yearToDateReturns: number
  bestPerformer: {
    propertyId: string
    roi: number
  }
  worstPerformer: {
    propertyId: string
    roi: number
  }
}

// Distribution Types
export type DistributionType = 'rental' | 'capital_gain' | 'dividend'
export type DistributionStatus = 'pending' | 'paid' | 'failed'

export interface Distribution {
  id: string
  userId: string
  propertyId: string
  amount: number
  type: DistributionType
  status: DistributionStatus
  distributionDate: string
  paidDate?: string
  description: string
}

// Transaction Types
export type TransactionType = 'investment' | 'distribution' | 'withdrawal' | 'fee'

export interface Transaction {
  id: string
  userId: string
  type: TransactionType
  amount: number
  currency: Currency
  status: 'pending' | 'completed' | 'failed'
  description: string
  propertyId?: string
  paymentMethod?: PaymentMethod
  transactionHash?: string // For crypto transactions
  walletAddress?: string // For crypto transactions
  createdAt: string
  completedAt?: string
}

// Market Data Types
export interface MarketData {
  propertyId: string
  currentPrice: number
  priceChange24h: number
  priceChangePercent24h: number
  volume24h: number
  marketCap: number
  lastUpdated: string
}

// Crypto Wallet Types
export interface CryptoWallet {
  id: string
  userId: string
  currency: Cryptocurrency
  address: string
  balance: number
  isVerified: boolean
  createdAt: string
  lastUsed?: string
}

// Payment Method Types
export interface PaymentMethodInfo {
  id: string
  userId: string
  type: PaymentMethod
  name: string
  details: FiatPaymentDetails | CryptoPaymentDetails
  isDefault: boolean
  isVerified: boolean
  createdAt: string
  lastUsed?: string
}

export interface FiatPaymentDetails {
  bankName?: string
  accountNumber?: string
  routingNumber?: string
  cardLast4?: string
  cardType?: 'visa' | 'mastercard' | 'amex'
  upiId?: string
  walletProvider?: string
}

export interface CryptoPaymentDetails {
  walletAddress: string
  walletType: CryptoPaymentMethod
  network: string
  isHardwareWallet?: boolean
}

// Currency Exchange Types
export interface ExchangeRate {
  from: Currency
  to: Currency
  rate: number
  lastUpdated: string
}

// Multi-Currency Balance Types
export interface WalletBalance {
  currency: Currency
  balance: number
  lockedBalance: number
  pendingBalance: number
}

// Secondary Market Types
export type OrderType = 'buy' | 'sell'
export type OrderStatus = 'active' | 'filled' | 'cancelled' | 'expired'

export interface SecondaryOrder {
  id: string
  userId: string
  propertyId: string
  type: OrderType
  tokens: number
  pricePerToken: number
  totalAmount: number
  status: OrderStatus
  createdAt: string
  expiresAt: string
  filledAt?: string
}

// Notification Types
export type NotificationType = 'info' | 'success' | 'warning' | 'error'

export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  message: string
  isRead: boolean
  createdAt: string
  actionUrl?: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Filter and Search Types
export interface PropertyFilters {
  type?: PropertyType[]
  location?: string[]
  priceRange?: [number, number]
  roiRange?: [number, number]
  status?: PropertyStatus[]
  developer?: string[]
  sortBy?: 'price' | 'roi' | 'funding_progress' | 'created_at'
  sortOrder?: 'asc' | 'desc'
}

export interface SearchParams {
  query?: string
  filters?: PropertyFilters
  page?: number
  limit?: number
}
