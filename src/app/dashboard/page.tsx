'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { PropertyCard } from '@/components/ui/PropertyCard'
import { useAuth, withAuth } from '@/contexts/AuthContext'

import { Button } from '@/components/ui/Button'
import { calculatePortfolio } from '@/data/mockInvestments'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatPercentage } from '@/lib/utils'
import {
    ArrowUpRight,
    Building2,
    Eye,
    Plus,
    TrendingUp,
    Users,
    Wallet
} from 'lucide-react'
import { useRouter } from 'next/navigation'

function DashboardPage() {
  const { user } = useAuth()
  const router = useRouter()

  if (!user) return null

  const renderInvestorDashboard = () => {
    const portfolio = calculatePortfolio(user.id)
    const featuredProperties = mockProperties.filter(p => p.status === 'active_funding').slice(0, 3)

    return (
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">Welcome back, {user.name}!</h1>
          <p className="opacity-90">
            {user.userType === 'institutional_investor' 
              ? 'Manage your institutional real estate portfolio'
              : 'Track your real estate investments and discover new opportunities'
            }
          </p>
        </div>

        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Invested</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(portfolio.totalInvested, user.profile.preferredCurrency)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Wallet className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Value</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(portfolio.currentValue, user.profile.preferredCurrency)}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Returns</p>
                  <div className="flex items-center space-x-2">
                    <p className="text-2xl font-bold text-green-600 financial-data">
                      {formatCompactCurrency(portfolio.totalReturns, user.profile.preferredCurrency)}
                    </p>
                    <ArrowUpRight className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <ArrowUpRight className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">ROI</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatPercentage(portfolio.roi)}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/properties')}
              >
                <Building2 className="h-6 w-6 mb-2" />
                Browse Properties
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/portfolio')}
              >
                <TrendingUp className="h-6 w-6 mb-2" />
                View Portfolio
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/secondary-market')}
              >
                <Users className="h-6 w-6 mb-2" />
                Secondary Market
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Featured Properties */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Featured Properties</CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push('/properties')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredProperties.map((property) => (
                <PropertyCard
                  key={property.id}
                  property={property}
                  onViewDetails={(id) => router.push(`/properties/${id}`)}
                  onInvest={(id) => router.push(`/properties/${id}/invest`)}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderDeveloperDashboard = () => {
    return (
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-xl p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">Welcome back, {user.name}!</h1>
          <p className="opacity-90">Manage your property listings and track investor engagement</p>
        </div>

        {/* Developer Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Properties</p>
                  <p className="text-2xl font-bold text-gray-900">3</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Building2 className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Funding</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">₹85Cr</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Wallet className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-green-600">90%</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Investors</p>
                  <p className="text-2xl font-bold text-gray-900">1,247</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="primary" 
                className="h-20 flex-col"
                onClick={() => router.push('/developer/add-property')}
              >
                <Plus className="h-6 w-6 mb-2" />
                Add New Property
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/developer/properties')}
              >
                <Building2 className="h-6 w-6 mb-2" />
                Manage Properties
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/developer/analytics')}
              >
                <TrendingUp className="h-6 w-6 mb-2" />
                View Analytics
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderAdminDashboard = () => {
    return (
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">Admin Dashboard</h1>
          <p className="opacity-90">Monitor platform performance and manage operations</p>
        </div>

        {/* Admin Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">15,247</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Properties</p>
                  <p className="text-2xl font-bold text-gray-900">47</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Building2 className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Platform AUM</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">₹895Cr</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Wallet className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending KYC</p>
                  <p className="text-2xl font-bold text-orange-600">12</p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <Eye className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/admin/users')}
              >
                <Users className="h-6 w-6 mb-2" />
                Manage Users
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/admin/properties')}
              >
                <Building2 className="h-6 w-6 mb-2" />
                Review Properties
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col"
                onClick={() => router.push('/admin/analytics')}
              >
                <TrendingUp className="h-6 w-6 mb-2" />
                Platform Analytics
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderDashboardContent = () => {
    switch (user.userType) {
      case 'retail_investor':
      case 'institutional_investor':
        return renderInvestorDashboard()
      case 'developer':
        return renderDeveloperDashboard()
      case 'admin':
        return renderAdminDashboard()
      default:
        return renderInvestorDashboard()
    }
  }

  return (
    <DashboardLayout>
      {renderDashboardContent()}
    </DashboardLayout>
  )
}

export default withAuth(DashboardPage)
