'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { calculatePortfolio } from '@/data/mockInvestments'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatDate, formatPercentage } from '@/lib/utils'
import {
    ArrowDownRight,
    ArrowUpRight,
    BarChart3,
    Building2,
    Calendar,
    Download,
    Eye,
    Filter,
    TrendingDown,
    TrendingUp,
    Wallet
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

function PortfolioPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'overview' | 'holdings' | 'distributions' | 'performance'>('overview')

  if (!user) return null

  const portfolio = calculatePortfolio(user.id)
  const currency = user.profile.preferredCurrency

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'holdings', label: 'Holdings' },
    { id: 'distributions', label: 'Distributions' },
    { id: 'performance', label: 'Performance' }
  ]

  const getPropertyById = (id: string) => {
    return mockProperties.find(p => p.id === id)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Portfolio</h1>
            <p className="text-gray-600 mt-1">
              Track your real estate investments and performance
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
          </div>
        </div>

        {/* Portfolio Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Invested</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(portfolio.totalInvested, currency)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Wallet className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Value</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(portfolio.currentValue, currency)}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Returns</p>
                  <div className="flex items-center space-x-2">
                    <p className="text-2xl font-bold text-green-600 financial-data">
                      {formatCompactCurrency(portfolio.totalReturns, currency)}
                    </p>
                    <ArrowUpRight className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <ArrowUpRight className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">ROI</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatPercentage(portfolio.roi)}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{portfolio.summary.totalProperties}</div>
              <div className="text-sm text-gray-600">Properties Invested</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 financial-data">
                {formatCompactCurrency(portfolio.summary.monthlyIncome, currency)}
              </div>
              <div className="text-sm text-gray-600">Monthly Income</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 financial-data">
                {formatCompactCurrency(portfolio.summary.yearToDateReturns, currency)}
              </div>
              <div className="text-sm text-gray-600">YTD Returns</div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 border-b border-gray-200">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Best and Worst Performers */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2 flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Best Performer
                    </h3>
                    {portfolio.summary.bestPerformer.propertyId && (
                      <div>
                        <div className="font-medium text-gray-900">
                          {getPropertyById(portfolio.summary.bestPerformer.propertyId)?.title}
                        </div>
                        <div className="text-sm text-green-600 font-semibold">
                          +{formatPercentage(portfolio.summary.bestPerformer.roi)} ROI
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="p-4 bg-red-50 rounded-lg">
                    <h3 className="font-semibold text-red-800 mb-2 flex items-center">
                      <TrendingDown className="h-4 w-4 mr-2" />
                      Lowest Performer
                    </h3>
                    {portfolio.summary.worstPerformer.propertyId && (
                      <div>
                        <div className="font-medium text-gray-900">
                          {getPropertyById(portfolio.summary.worstPerformer.propertyId)?.title}
                        </div>
                        <div className="text-sm text-red-600 font-semibold">
                          +{formatPercentage(portfolio.summary.worstPerformer.roi)} ROI
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Recent Activity */}
                <div>
                  <h3 className="font-semibold text-gray-900 mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {portfolio.distributions.slice(0, 3).map((distribution) => {
                      const property = getPropertyById(distribution.propertyId)
                      return (
                        <div key={distribution.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                              <ArrowDownRight className="h-5 w-5 text-green-600" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">Distribution Received</div>
                              <div className="text-sm text-gray-500">{property?.title}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-green-600 financial-data">
                              +{formatCompactCurrency(distribution.amount, currency)}
                            </div>
                            <div className="text-sm text-gray-500">
                              {formatDate(distribution.distributionDate)}
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'holdings' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Your Holdings</h3>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>

                <div className="space-y-4">
                  {portfolio.investments.map((investment) => {
                    const property = getPropertyById(investment.propertyId)
                    if (!property) return null

                    return (
                      <div key={investment.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-medium text-gray-900">{property.title}</h4>
                              <Badge variant={investment.roi >= 0 ? 'success' : 'error'}>
                                {investment.roi >= 0 ? '+' : ''}{formatPercentage(investment.roi)}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{property.location.city}, {property.location.country}</p>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <div className="text-gray-600">Invested</div>
                                <div className="font-semibold financial-data">
                                  {formatCompactCurrency(investment.amount, currency)}
                                </div>
                              </div>
                              <div>
                                <div className="text-gray-600">Current Value</div>
                                <div className="font-semibold financial-data">
                                  {formatCompactCurrency(investment.currentValue, currency)}
                                </div>
                              </div>
                              <div>
                                <div className="text-gray-600">Tokens</div>
                                <div className="font-semibold">{investment.tokens}</div>
                              </div>
                              <div>
                                <div className="text-gray-600">Returns</div>
                                <div className={`font-semibold ${investment.totalReturns >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {investment.totalReturns >= 0 ? '+' : ''}{formatCompactCurrency(investment.totalReturns, currency)}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="ml-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/properties/${property.id}`)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {activeTab === 'distributions' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Distribution History</h3>
                  <div className="text-sm text-gray-600">
                    Total Received: <span className="font-semibold financial-data">
                      {formatCompactCurrency(
                        portfolio.distributions.reduce((sum, d) => sum + d.amount, 0),
                        currency
                      )}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  {portfolio.distributions.map((distribution) => {
                    const property = getPropertyById(distribution.propertyId)
                    return (
                      <div key={distribution.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            distribution.status === 'paid' ? 'bg-green-100' : 'bg-yellow-100'
                          }`}>
                            <ArrowDownRight className={`h-5 w-5 ${
                              distribution.status === 'paid' ? 'text-green-600' : 'text-yellow-600'
                            }`} />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{property?.title}</div>
                            <div className="text-sm text-gray-500">
                              {distribution.type === 'rental' ? 'Rental Income' : 'Capital Gain'} • 
                              {formatDate(distribution.distributionDate)}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-green-600 financial-data">
                            +{formatCompactCurrency(distribution.amount, currency)}
                          </div>
                          <Badge variant={distribution.status === 'paid' ? 'success' : 'warning'} size="sm">
                            {distribution.status}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {activeTab === 'performance' && (
              <div className="space-y-6">
                <div className="text-center p-8 bg-gray-50 rounded-lg">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Analytics</h3>
                  <p className="text-gray-600 mb-4">
                    Detailed performance charts and analytics will be available here.
                  </p>
                  <Button variant="outline">
                    View Detailed Analytics
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(PortfolioPage, ['retail_investor', 'institutional_investor'])
