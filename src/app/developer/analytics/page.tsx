'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getInvestmentsByProperty } from '@/data/mockInvestments'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatPercentage } from '@/lib/utils'
import {
    ArrowUpRight,
    BarChart3,
    Building2,
    DollarSign,
    Download,
    Eye,
    Filter,
    PieChart,
    Target,
    Users
} from 'lucide-react'
import { useState } from 'react'

function DeveloperAnalyticsPage() {
  const { user } = useAuth()
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')
  const [selectedProperty, setSelectedProperty] = useState<string>('all')

  if (!user) return null

  // Get properties for this developer (mock: using Gujarat State Development Corp)
  const developerProperties = mockProperties.filter(p => p.developer.id === 'dev_gujarat_001')
  
  // Calculate analytics
  const totalProperties = developerProperties.length
  const activeProperties = developerProperties.filter(p => p.status === 'active_funding').length
  const completedProperties = developerProperties.filter(p => p.status === 'completed').length
  
  const totalInvestments = developerProperties.reduce((sum, property) => {
    const investments = getInvestmentsByProperty(property.id)
    return sum + investments.reduce((propSum, inv) => propSum + inv.amount, 0)
  }, 0)

  const totalInvestors = developerProperties.reduce((sum, property) => {
    const investments = getInvestmentsByProperty(property.id)
    return sum + investments.length
  }, 0)

  const avgFundingProgress = developerProperties.reduce((sum, p) => sum + p.tokenomics.fundingProgress, 0) / totalProperties

  // Mock performance data
  const performanceData = [
    { month: 'Jan', investments: 2500000, investors: 45 },
    { month: 'Feb', investments: 3200000, investors: 62 },
    { month: 'Mar', investments: 2800000, investors: 51 },
    { month: 'Apr', investments: 4100000, investors: 78 },
    { month: 'May', investments: 3600000, investors: 69 },
    { month: 'Jun', investments: 4500000, investors: 85 },
  ]

  const propertyPerformance = developerProperties.map(property => {
    const investments = getInvestmentsByProperty(property.id)
    const totalRaised = investments.reduce((sum, inv) => sum + inv.amount, 0)
    const investorCount = investments.length
    
    return {
      id: property.id,
      title: property.title,
      totalRaised,
      investorCount,
      fundingProgress: property.tokenomics.fundingProgress,
      expectedROI: property.financials.expectedROI,
      status: property.status
    }
  })

  const timeRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Track your property performance and investor engagement
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d' | '1y')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              {timeRangeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Button variant="outline" leftIcon={<Download className="h-4 w-4" />}>
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Raised</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(totalInvestments, 'INR')}
                  </p>
                  <div className="flex items-center mt-1">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+12.5%</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Investors</p>
                  <p className="text-2xl font-bold text-gray-900">{totalInvestors}</p>
                  <div className="flex items-center mt-1">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+8.2%</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Properties</p>
                  <p className="text-2xl font-bold text-gray-900">{activeProperties}</p>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-600">of {totalProperties} total</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Building2 className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Funding</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(avgFundingProgress)}
                  </p>
                  <div className="flex items-center mt-1">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+5.1%</span>
                  </div>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <Target className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Investment Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Investment Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Investment trends chart would be displayed here</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Showing monthly investment volume and investor count
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Property Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Property Status Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                    <span className="text-sm text-gray-600">Active Funding</span>
                  </div>
                  <span className="text-sm font-medium">{activeProperties}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                    <span className="text-sm text-gray-600">Completed</span>
                  </div>
                  <span className="text-sm font-medium">{completedProperties}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-600 rounded-full"></div>
                    <span className="text-sm text-gray-600">Under Review</span>
                  </div>
                  <span className="text-sm font-medium">0</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span className="text-sm text-gray-600">Draft</span>
                  </div>
                  <span className="text-sm font-medium">0</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Property Performance Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Property Performance</CardTitle>
              <div className="flex items-center space-x-2">
                <select
                  value={selectedProperty}
                  onChange={(e) => setSelectedProperty(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm"
                >
                  <option value="all">All Properties</option>
                  {developerProperties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.title}
                    </option>
                  ))}
                </select>
                <Button variant="outline" size="sm" leftIcon={<Filter className="h-4 w-4" />}>
                  Filter
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Property</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Funding Progress</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Total Raised</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Investors</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Expected ROI</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {propertyPerformance.map((property) => (
                    <tr key={property.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{property.title}</div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={
                          property.status === 'active_funding' ? 'info' :
                          property.status === 'completed' ? 'success' :
                          property.status === 'fully_funded' ? 'success' : 'default'
                        }>
                          {property.status.split('_').map(word => 
                            word.charAt(0).toUpperCase() + word.slice(1)
                          ).join(' ')}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min(property.fundingProgress, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">
                            {formatPercentage(property.fundingProgress)}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="font-medium financial-data">
                          {formatCompactCurrency(property.totalRaised, 'INR')}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="font-medium">{property.investorCount}</span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="font-medium text-green-600">
                          {formatPercentage(property.expectedROI)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />}>
                          View
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-3 bg-green-50 rounded-lg">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <ArrowUpRight className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">New Investment Received</div>
                  <div className="text-sm text-gray-600">₹2.5L investment in GIFT City IT Hub</div>
                </div>
                <div className="text-sm text-gray-500">2 hours ago</div>
              </div>
              
              <div className="flex items-center space-x-4 p-3 bg-blue-50 rounded-lg">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">New Investor Joined</div>
                  <div className="text-sm text-gray-600">Institutional investor showed interest</div>
                </div>
                <div className="text-sm text-gray-500">5 hours ago</div>
              </div>
              
              <div className="flex items-center space-x-4 p-3 bg-purple-50 rounded-lg">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Target className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">Funding Milestone Reached</div>
                  <div className="text-sm text-gray-600">GIFT City IT Hub reached 25% funding</div>
                </div>
                <div className="text-sm text-gray-500">1 day ago</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(DeveloperAnalyticsPage, ['developer'])
