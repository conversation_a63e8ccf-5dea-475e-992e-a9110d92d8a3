'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { mockProperties } from '@/data/mockProperties'
import {
    ArrowLeft,
    Building2,
    CheckCircle,
    DollarSign,
    FileText,
    Info,
    MapPin,
    Save,
    Upload
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

function EditPropertyPage({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth()
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)
  const [propertyId, setPropertyId] = useState<string>('')

  // Initialize form data with default values
  const [formData, setFormData] = useState<{
    title: string;
    description: string;
    type: 'residential' | 'commercial' | 'mixed_use';
    address: string;
    city: string;
    state: string;
    country: string;
    size: string;
    sizeUnit: string;
    yearBuilt: string;
    bedrooms: string;
    bathrooms: string;
    parking: string;
    totalValue: string;
    expectedROI: string;
    rentalYield: string;
    appreciationRate: string;
    operatingExpenses: string;
    managementFee: string;
    platformFee: string;
    totalTokens: string;
    tokenPrice: string;
    minimumInvestment: string;
  }>({
    title: '',
    description: '',
    type: 'residential',
    address: '',
    city: '',
    state: '',
    country: '',
    size: '',
    sizeUnit: 'sqft',
    yearBuilt: '',
    bedrooms: '',
    bathrooms: '',
    parking: '',
    totalValue: '',
    expectedROI: '',
    rentalYield: '',
    appreciationRate: '',
    operatingExpenses: '',
    managementFee: '',
    platformFee: '',
    totalTokens: '',
    tokenPrice: '',
    minimumInvestment: ''
  })

  // Handle async params
  React.useEffect(() => {
    params.then(resolvedParams => {
      setPropertyId(resolvedParams.id)
    })
  }, [params])

  const property = mockProperties.find(p => p.id === propertyId)

  // Update form data when property is loaded
  React.useEffect(() => {
    if (property) {
      setFormData({
        title: property.title,
        description: property.description,
        type: property.type,
        address: property.location.address,
        city: property.location.city,
        state: property.location.state,
        country: property.location.country,
        size: property.metrics.size.toString(),
        sizeUnit: property.metrics.sizeUnit,
        yearBuilt: property.metrics.yearBuilt?.toString() || '',
        bedrooms: property.metrics.bedrooms?.toString() || '',
        bathrooms: property.metrics.bathrooms?.toString() || '',
        parking: property.metrics.parking?.toString() || '',
        totalValue: property.financials.totalValue.toString(),
        expectedROI: property.financials.expectedROI.toString(),
        rentalYield: property.financials.rentalYield.toString(),
        appreciationRate: property.financials.appreciationRate.toString(),
        operatingExpenses: property.financials.operatingExpenses.toString(),
        managementFee: property.financials.managementFee.toString(),
        platformFee: property.financials.platformFee.toString(),
        totalTokens: property.tokenomics.totalTokens.toString(),
        tokenPrice: property.tokenomics.tokenPrice.toString(),
        minimumInvestment: property.tokenomics.minimumInvestment.toString()
      })
    }
  }, [property])

  if (!user) return null

  if (!propertyId) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading...</h2>
          <p className="text-gray-600">Loading property details...</p>
        </div>
      </DashboardLayout>
    )
  }

  if (!property) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Property Not Found</h2>
          <p className="text-gray-600 mb-4">The property you&apos;re looking for doesn&apos;t exist.</p>
          <Button variant="outline" onClick={() => router.push('/developer/properties')}>
            Back to Properties
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  // Form data is now initialized at the top of the component

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsSaving(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsSaving(false)
    alert('Property updated successfully!')
    router.push('/developer/properties')
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => router.push('/developer/properties')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Properties
          </Button>
          <div className="flex items-center space-x-2">
            <Badge variant={
              property.status === 'active_funding' ? 'info' :
              property.status === 'completed' ? 'success' :
              property.status === 'fully_funded' ? 'success' : 'default'
            }>
              {property.status.split('_').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ')}
            </Badge>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={isSaving}
              leftIcon={<Save className="h-4 w-4" />}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="Property Title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="e.g., Luxury Apartments in Bandra West"
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe your property..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Type
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="residential">Residential</option>
                    <option value="commercial">Commercial</option>
                    <option value="mixed_use">Mixed Use</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Location */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Location
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="Address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Complete address"
                />
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    label="City"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="City"
                  />
                  <Input
                    label="State"
                    value={formData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    placeholder="State"
                  />
                  <Input
                    label="Country"
                    value={formData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    placeholder="Country"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Property Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building2 className="h-5 w-5 mr-2" />
                  Property Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex space-x-2">
                    <Input
                      label="Size"
                      value={formData.size}
                      onChange={(e) => handleInputChange('size', e.target.value)}
                      placeholder="1000"
                      type="number"
                    />
                    <div className="w-24">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                      <select
                        value={formData.sizeUnit}
                        onChange={(e) => handleInputChange('sizeUnit', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="sqft">Sq Ft</option>
                        <option value="sqm">Sq M</option>
                      </select>
                    </div>
                  </div>
                  <Input
                    label="Year Built"
                    value={formData.yearBuilt}
                    onChange={(e) => handleInputChange('yearBuilt', e.target.value)}
                    placeholder="2024"
                    type="number"
                  />
                </div>

                {formData.type === 'residential' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      label="Bedrooms"
                      value={formData.bedrooms}
                      onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                      placeholder="3"
                      type="number"
                    />
                    <Input
                      label="Bathrooms"
                      value={formData.bathrooms}
                      onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                      placeholder="2"
                      type="number"
                    />
                    <Input
                      label="Parking Spaces"
                      value={formData.parking}
                      onChange={(e) => handleInputChange('parking', e.target.value)}
                      placeholder="1"
                      type="number"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Financial Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Financial Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Total Property Value"
                    value={formData.totalValue}
                    onChange={(e) => handleInputChange('totalValue', e.target.value)}
                    placeholder="50000000"
                    type="number"
                  />
                  <Input
                    label="Token Price"
                    value={formData.tokenPrice}
                    onChange={(e) => handleInputChange('tokenPrice', e.target.value)}
                    placeholder="10000"
                    type="number"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Expected ROI (%)"
                    value={formData.expectedROI}
                    onChange={(e) => handleInputChange('expectedROI', e.target.value)}
                    placeholder="12"
                    type="number"
                  />
                  <Input
                    label="Rental Yield (%)"
                    value={formData.rentalYield}
                    onChange={(e) => handleInputChange('rentalYield', e.target.value)}
                    placeholder="4.5"
                    type="number"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Property Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Current Status</span>
                    <Badge variant={
                      property.status === 'active_funding' ? 'info' :
                      property.status === 'completed' ? 'success' :
                      property.status === 'fully_funded' ? 'success' : 'default'
                    }>
                      {property.status.split('_').map(word => 
                        word.charAt(0).toUpperCase() + word.slice(1)
                      ).join(' ')}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Funding Progress</span>
                    <span className="font-medium">{property.tokenomics.fundingProgress.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Raised</span>
                    <span className="font-medium">₹{(property.tokenomics.fundingProgress * property.tokenomics.fundingTarget / 100).toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full" leftIcon={<FileText className="h-4 w-4" />}>
                    View Documents
                  </Button>
                  <Button variant="outline" className="w-full" leftIcon={<Upload className="h-4 w-4" />}>
                    Upload Images
                  </Button>
                  <Button variant="outline" className="w-full" leftIcon={<CheckCircle className="h-4 w-4" />}>
                    View Analytics
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Help */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Need Help?</p>
                    <p>Contact our support team if you need assistance with property management.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(EditPropertyPage, ['developer'])
