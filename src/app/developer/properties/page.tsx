'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getInvestmentsByProperty } from '@/data/mockInvestments'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatDate, formatPercentage } from '@/lib/utils'
import {
    Building2,
    Calendar,
    DollarSign,
    Edit,
    Eye,
    Filter,
    MapPin,
    MoreVertical,
    Plus,
    Search,
    TrendingUp,
    Users
} from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

function DeveloperPropertiesPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  if (!user) return null

  // Get properties for this developer (mock: using Gujarat State Development Corp)
  const developerProperties = mockProperties.filter(p => p.developer.id === 'dev_gujarat_001')
  
  const filteredProperties = developerProperties.filter(property => {
    const matchesSearch = searchQuery === '' || 
      property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      property.location.city.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const getPropertyStats = (propertyId: string) => {
    const investments = getInvestmentsByProperty(propertyId)
    const totalInvestors = investments.length
    const totalRaised = investments.reduce((sum, inv) => sum + inv.amount, 0)
    return { totalInvestors, totalRaised }
  }

  const formatStatus = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active_funding': return 'info'
      case 'fully_funded': return 'success'
      case 'completed': return 'success'
      case 'under_review': return 'warning'
      case 'draft': return 'default'
      default: return 'default'
    }
  }

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'draft', label: 'Draft' },
    { value: 'under_review', label: 'Under Review' },
    { value: 'active_funding', label: 'Active Funding' },
    { value: 'fully_funded', label: 'Fully Funded' },
    { value: 'completed', label: 'Completed' }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Properties</h1>
            <p className="text-gray-600 mt-1">
              Manage your property listings and track performance
            </p>
          </div>
          <Button 
            variant="primary" 
            leftIcon={<Plus className="h-4 w-4" />}
            onClick={() => router.push('/developer/add-property')}
          >
            Add New Property
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{developerProperties.length}</div>
              <div className="text-sm text-gray-600">Total Properties</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {developerProperties.filter(p => p.status === 'active_funding').length}
              </div>
              <div className="text-sm text-gray-600">Active Funding</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">1,247</div>
              <div className="text-sm text-gray-600">Total Investors</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <DollarSign className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 financial-data">₹85Cr</div>
              <div className="text-sm text-gray-600">Total Raised</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search properties..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Properties List */}
        <div className="space-y-4">
          {filteredProperties.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
                <p className="text-gray-600 mb-4">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'Try adjusting your search criteria or filters.'
                    : 'Get started by adding your first property listing.'
                  }
                </p>
                <Button 
                  variant="primary"
                  onClick={() => router.push('/developer/add-property')}
                >
                  Add New Property
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredProperties.map((property) => {
              const stats = getPropertyStats(property.id)
              
              return (
                <Card key={property.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        {/* Property Image */}
                        <div className="w-24 h-24 overflow-hidden rounded-lg flex-shrink-0">
                          <Image
                            src={property.images[0]}
                            alt={property.title}
                            width={96}
                            height={96}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        
                        {/* Property Details */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                {property.title}
                              </h3>
                              <p className="text-sm text-gray-600 flex items-center mb-2">
                                <MapPin className="h-4 w-4 mr-1" />
                                {property.location.city}, {property.location.country}
                              </p>
                              <Badge variant={getStatusVariant(property.status)}>
                                {formatStatus(property.status)}
                              </Badge>
                            </div>
                          </div>
                          
                          {/* Property Metrics */}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                            <div>
                              <div className="text-sm text-gray-600">Funding Progress</div>
                              <div className="font-semibold text-blue-600">
                                {formatPercentage(property.tokenomics.fundingProgress)}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Total Raised</div>
                              <div className="font-semibold financial-data">
                                {formatCompactCurrency(stats.totalRaised, property.financials.currency)}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Investors</div>
                              <div className="font-semibold">{stats.totalInvestors}</div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Expected ROI</div>
                              <div className="font-semibold text-green-600">
                                {formatPercentage(property.financials.expectedROI)}
                              </div>
                            </div>
                          </div>
                          
                          {/* Funding Progress Bar */}
                          <div className="mt-4">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>Funding Progress</span>
                              <span>{formatPercentage(property.tokenomics.fundingProgress)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min(property.tokenomics.fundingProgress, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                          
                          {/* Timeline */}
                          {property.timeline.fundingEndDate && (
                            <div className="mt-3 flex items-center text-sm text-gray-600">
                              <Calendar className="h-4 w-4 mr-2" />
                              <span>
                                Funding ends: {formatDate(property.timeline.fundingEndDate)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Actions */}
                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/properties/${property.id}`)}
                          leftIcon={<Eye className="h-4 w-4" />}
                        >
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/developer/properties/${property.id}/edit`)}
                          leftIcon={<Edit className="h-4 w-4" />}
                        >
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>

        {/* Load More */}
        {filteredProperties.length > 0 && filteredProperties.length >= 5 && (
          <div className="text-center">
            <Button variant="outline" size="lg">
              Load More Properties
            </Button>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}

export default withAuth(DeveloperPropertiesPage, ['developer'])
