'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getInvestmentsByProperty } from '@/data/mockInvestments'
import { mockProperties } from '@/data/mockProperties'
import { mockUsers } from '@/data/mockUsers'
import { formatCompactCurrency } from '@/lib/utils'
import {
    Building2,
    Download,
    Eye,
    Filter,
    Mail,
    MapPin,
    MessageCircle,
    Phone,
    Search,
    Star,
    TrendingUp,
    Users
} from 'lucide-react'
import { useState } from 'react'

function DeveloperInvestorsPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProperty, setSelectedProperty] = useState<string>('all')
  const [investorTypeFilter, setInvestorTypeFilter] = useState<string>('all')

  if (!user) return null

  // Get properties for this developer (mock: using Gujarat State Development Corp)
  const developerProperties = mockProperties.filter(p => p.developer.id === 'dev_gujarat_001')
  
  // Get all investors for developer's properties
  const allInvestors = developerProperties.reduce((investors, property) => {
    const investments = getInvestmentsByProperty(property.id)
    investments.forEach(investment => {
      const investor = mockUsers.find(u => u.id === investment.userId)
      if (investor) {
        const existingInvestor = investors.find(inv => inv.id === investor.id)
        if (existingInvestor) {
          existingInvestor.totalInvestment += investment.amount
          existingInvestor.properties.push({
            propertyId: property.id,
            propertyName: property.title,
            investment: investment.amount,
            tokens: investment.tokens,
            date: investment.investedAt
          })
        } else {
          investors.push({
            id: investor.id,
            name: investor.name,
            email: investor.email,
            totalInvestment: investment.amount,
            properties: [{
              propertyId: property.id,
              propertyName: property.title,
              investment: investment.amount,
              tokens: investment.tokens,
              date: investment.investedAt
            }],
            joinDate: investor.createdAt,
            status: investor.kycStatus || 'pending',
            avatar: investor.avatar,
            userType: investor.userType,
            profile: investor.profile
          })
        }
      }
    })
    return investors
  }, [] as Array<{ id: string; name: string; email: string; totalInvestment: number; properties: Array<{ propertyId: string; propertyName: string; investment: number; tokens: number; date?: string }>; joinDate: string; status: string; avatar?: string; userType?: string; profile?: { phone?: string; address?: string; city?: string; country?: string } }>)

  const filteredInvestors = allInvestors.filter(investor => {
    const matchesSearch = searchQuery === '' ||
      investor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      investor.email.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesProperty = selectedProperty === 'all' ||
      investor.properties.some((p: { propertyId: string }) => p.propertyId === selectedProperty)

    const matchesType = investorTypeFilter === 'all' || investor.userType === investorTypeFilter

    return matchesSearch && matchesProperty && matchesType
  })

  // Action handlers
  const handleViewProfile = (investorId: string) => {
    alert(`View Profile for investor: ${investorId}`)
  }

  const handleSendMessage = (investorId: string, investorName: string) => {
    alert(`Send Message to: ${investorName}`)
  }

  const handleSendEmail = (investorId: string, email: string) => {
    alert(`Send Email to: ${email}`)
  }

  const handleExportData = () => {
    alert('Export investor data - In a real app, this would download a CSV/Excel file')
  }

  const handleSendBulkUpdate = () => {
    alert('Send bulk update to all investors')
  }

  const totalInvestors = allInvestors.length
  const totalInvestment = allInvestors.reduce((sum, inv) => sum + inv.totalInvestment, 0)
  const avgInvestment = totalInvestors > 0 ? totalInvestment / totalInvestors : 0
  const institutionalInvestors = allInvestors.filter(inv => inv.userType === 'institutional_investor').length

  const getUserTypeLabel = (userType?: string) => {
    switch (userType) {
      case 'retail_investor': return 'Retail'
      case 'institutional_investor': return 'Institutional'
      default: return 'Investor'
    }
  }

  const getUserTypeColor = (userType?: string) => {
    switch (userType) {
      case 'retail_investor': return 'info'
      case 'institutional_investor': return 'secondary'
      default: return 'default'
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Investor Relations</h1>
            <p className="text-gray-600 mt-1">
              Manage relationships with your property investors
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <Button variant="outline" leftIcon={<Download className="h-4 w-4" />}>
              Export List
            </Button>
            <Button variant="primary" leftIcon={<MessageCircle className="h-4 w-4" />}>
              Send Update
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{totalInvestors}</div>
              <div className="text-sm text-gray-600">Total Investors</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 financial-data">
                {formatCompactCurrency(totalInvestment, 'INR')}
              </div>
              <div className="text-sm text-gray-600">Total Investment</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Star className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 financial-data">
                {formatCompactCurrency(avgInvestment, 'INR')}
              </div>
              <div className="text-sm text-gray-600">Avg Investment</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Building2 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{institutionalInvestors}</div>
              <div className="text-sm text-gray-600">Institutional</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search investors by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedProperty}
                  onChange={(e) => setSelectedProperty(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Properties</option>
                  {developerProperties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.title}
                    </option>
                  ))}
                </select>
                <select
                  value={investorTypeFilter}
                  onChange={(e) => setInvestorTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Types</option>
                  <option value="retail_investor">Retail</option>
                  <option value="institutional_investor">Institutional</option>
                </select>
                <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Investors List */}
        <Card>
          <CardHeader>
            <CardTitle>Investor Directory</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredInvestors.map((investor) => (
                <div key={investor.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-lg font-semibold">
                          {investor.name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{investor.name}</h3>
                          <Badge variant={getUserTypeColor(investor.userType) as 'default' | 'success' | 'warning' | 'error' | 'info' | 'secondary'}>
                            {getUserTypeLabel(investor.userType)}
                          </Badge>
                          {investor.status === 'verified' && (
                            <Badge variant="success" size="sm">Verified</Badge>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4" />
                            <span>{investor.email}</span>
                          </div>
                          {investor.profile?.phone && (
                            <div className="flex items-center space-x-2">
                              <Phone className="h-4 w-4" />
                              <span>{investor.profile.phone}</span>
                            </div>
                          )}
                          {investor.profile?.address && (
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4" />
                              <span className="truncate">{investor.profile.address}</span>
                            </div>
                          )}
                          <div className="flex items-center space-x-2">
                            <TrendingUp className="h-4 w-4" />
                            <span className="financial-data">
                              Total: {formatCompactCurrency(investor.totalInvestment, 'INR')}
                            </span>
                          </div>
                        </div>

                        {/* Investment Properties */}
                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            Investments ({investor.properties.length} properties)
                          </h4>
                          <div className="space-y-2">
                            {investor.properties.slice(0, 2).map((property: { propertyId: string; propertyName: string; investment: number; tokens: number; date?: string }) => (
                              <div key={property.propertyId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                  <div className="font-medium text-gray-900">{property.propertyName}</div>
                                  <div className="text-sm text-gray-500">
                                    {property.tokens.toLocaleString()} tokens
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="font-semibold text-gray-900 financial-data">
                                    {formatCompactCurrency(property.investment, 'INR')}
                                  </div>
                                </div>
                              </div>
                            ))}
                            {investor.properties.length > 2 && (
                              <div className="text-sm text-gray-500 text-center py-2">
                                +{investor.properties.length - 2} more properties
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />} onClick={() => handleViewProfile(investor.id)}>
                        View Profile
                      </Button>
                      <Button variant="outline" size="sm" leftIcon={<MessageCircle className="h-4 w-4" />} onClick={() => handleSendMessage(investor.id, investor.name)}>
                        Message
                      </Button>
                      <Button variant="outline" size="sm" leftIcon={<Mail className="h-4 w-4" />} onClick={() => handleSendEmail(investor.id, investor.email)}>
                        Email
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredInvestors.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No investors found</h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-16 flex-col" onClick={handleSendBulkUpdate}>
                <MessageCircle className="h-6 w-6 mb-2" />
                Send Newsletter
              </Button>
              <Button variant="outline" className="h-16 flex-col" onClick={handleExportData}>
                <Download className="h-6 w-6 mb-2" />
                Export Contacts
              </Button>
              <Button variant="outline" className="h-16 flex-col" onClick={() => alert('Performance Report - In a real app, this would generate a detailed report')}>
                <TrendingUp className="h-6 w-6 mb-2" />
                Performance Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(DeveloperInvestorsPage, ['developer'])
