'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { withAuth } from '@/contexts/AuthContext'
import {
    AlertCircle,
    ArrowLeft,
    Building2,
    Calendar,
    CheckCircle,
    DollarSign,
    FileText,
    Info,
    MapPin,
    Upload
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

function AddPropertyPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Basic Information
    title: '',
    description: '',
    type: 'residential' as 'residential' | 'commercial',
    
    // Location
    address: '',
    city: '',
    state: '',
    country: 'India',
    
    // Property Details
    size: '',
    sizeUnit: 'sqft' as 'sqft' | 'sqm',
    bedrooms: '',
    bathrooms: '',
    parking: '',
    yearBuilt: '',
    
    // Financial Details
    totalValue: '',
    currency: 'INR' as 'INR' | 'USD',
    expectedROI: '',
    rentalYield: '',
    
    // Tokenomics
    tokenPrice: '',
    minimumInvestment: '',
    maximumInvestment: '',
    fundingTarget: '',
    fundingDeadline: '',
    
    // Documents
    documents: [] as File[],
    images: [] as File[]
  })

  const steps = [
    { id: 1, title: 'Basic Information', description: 'Property details and description' },
    { id: 2, title: 'Location & Features', description: 'Address and property features' },
    { id: 3, title: 'Financial Details', description: 'Pricing and returns' },
    { id: 4, title: 'Tokenomics', description: 'Investment structure' },
    { id: 5, title: 'Documents & Media', description: 'Upload files and images' },
    { id: 6, title: 'Review & Submit', description: 'Final review before submission' }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleFileUpload = (field: 'documents' | 'images', files: FileList | null) => {
    if (files) {
      const newFiles = Array.from(files)
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], ...newFiles]
      }))

      // Show success message
      alert(`${newFiles.length} ${field} uploaded successfully! (This is a demo - files are not actually uploaded)`)
    }
  }

  const handleRemoveFile = (field: 'documents' | 'images', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.title && formData.description && formData.type)
      case 2:
        return !!(formData.address && formData.city && formData.state && formData.size)
      case 3:
        return !!(formData.totalValue && formData.expectedROI && formData.rentalYield)
      case 4:
        return !!(formData.tokenPrice && formData.minimumInvestment && formData.fundingTarget && formData.fundingDeadline)
      case 5:
        return formData.images.length > 0
      default:
        return true
    }
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length))
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    // Simulate submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    router.push('/developer/properties')
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => router.push('/developer/properties')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Properties
          </Button>
          <div className="text-sm text-gray-500">
            Step {currentStep} of {steps.length}
          </div>
        </div>

        {/* Progress Steps */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      step.id < currentStep
                        ? 'bg-green-600 text-white'
                        : step.id === currentStep
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {step.id < currentStep ? <CheckCircle className="h-5 w-5" /> : step.id}
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-16 h-1 mx-2 ${
                        step.id < currentStep ? 'bg-green-600' : 'bg-gray-200'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900">
                {steps[currentStep - 1].title}
              </h2>
              <p className="text-gray-600">{steps[currentStep - 1].description}</p>
            </div>
          </CardContent>
        </Card>

        {/* Form Content */}
        <Card>
          <CardContent className="p-6">
            {currentStep === 1 && (
              <div className="space-y-6">
                <Input
                  label="Property Title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="e.g., Luxury Apartments in Bandra West"
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe your property, its features, and investment potential..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Type
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      type="button"
                      onClick={() => handleInputChange('type', 'residential')}
                      className={`p-4 border-2 rounded-lg text-left transition-all ${
                        formData.type === 'residential'
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <Building2 className="h-6 w-6 text-gray-600 mb-2" />
                      <div className="font-medium">Residential</div>
                      <div className="text-sm text-gray-500">Apartments, villas, homes</div>
                    </button>
                    <button
                      type="button"
                      onClick={() => handleInputChange('type', 'commercial')}
                      className={`p-4 border-2 rounded-lg text-left transition-all ${
                        formData.type === 'commercial'
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <Building2 className="h-6 w-6 text-gray-600 mb-2" />
                      <div className="font-medium">Commercial</div>
                      <div className="text-sm text-gray-500">Offices, retail, warehouses</div>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-6">
                <Input
                  label="Address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Complete address"
                  leftIcon={<MapPin className="h-4 w-4" />}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    label="City"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="City"
                  />
                  <Input
                    label="State"
                    value={formData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    placeholder="State"
                  />
                  <Input
                    label="Country"
                    value={formData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    placeholder="Country"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex space-x-2">
                    <Input
                      label="Size"
                      value={formData.size}
                      onChange={(e) => handleInputChange('size', e.target.value)}
                      placeholder="1000"
                      type="number"
                    />
                    <div className="w-24">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                      <select
                        value={formData.sizeUnit}
                        onChange={(e) => handleInputChange('sizeUnit', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="sqft">Sq Ft</option>
                        <option value="sqm">Sq M</option>
                      </select>
                    </div>
                  </div>
                  <Input
                    label="Year Built"
                    value={formData.yearBuilt}
                    onChange={(e) => handleInputChange('yearBuilt', e.target.value)}
                    placeholder="2024"
                    type="number"
                  />
                </div>

                {formData.type === 'residential' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      label="Bedrooms"
                      value={formData.bedrooms}
                      onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                      placeholder="3"
                      type="number"
                    />
                    <Input
                      label="Bathrooms"
                      value={formData.bathrooms}
                      onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                      placeholder="2"
                      type="number"
                    />
                    <Input
                      label="Parking Spaces"
                      value={formData.parking}
                      onChange={(e) => handleInputChange('parking', e.target.value)}
                      placeholder="1"
                      type="number"
                    />
                  </div>
                )}
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex space-x-2">
                    <Input
                      label="Total Property Value"
                      value={formData.totalValue}
                      onChange={(e) => handleInputChange('totalValue', e.target.value)}
                      placeholder="50000000"
                      type="number"
                      leftIcon={<DollarSign className="h-4 w-4" />}
                    />
                    <div className="w-24">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                      <select
                        value={formData.currency}
                        onChange={(e) => handleInputChange('currency', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Expected ROI (%)"
                    value={formData.expectedROI}
                    onChange={(e) => handleInputChange('expectedROI', e.target.value)}
                    placeholder="12"
                    type="number"
                    helperText="Annual return on investment percentage"
                  />
                  <Input
                    label="Rental Yield (%)"
                    value={formData.rentalYield}
                    onChange={(e) => handleInputChange('rentalYield', e.target.value)}
                    placeholder="4.5"
                    type="number"
                    helperText="Annual rental income as % of property value"
                  />
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium mb-1">Financial Projections</p>
                      <p>These figures will be used to calculate investor returns and should be based on realistic market analysis and professional valuations.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Token Price"
                    value={formData.tokenPrice}
                    onChange={(e) => handleInputChange('tokenPrice', e.target.value)}
                    placeholder="10000"
                    type="number"
                    helperText="Price per token in selected currency"
                  />
                  <Input
                    label="Funding Target"
                    value={formData.fundingTarget}
                    onChange={(e) => handleInputChange('fundingTarget', e.target.value)}
                    placeholder="50000000"
                    type="number"
                    helperText="Total amount to raise"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Minimum Investment"
                    value={formData.minimumInvestment}
                    onChange={(e) => handleInputChange('minimumInvestment', e.target.value)}
                    placeholder="10000"
                    type="number"
                    helperText="Minimum amount per investor"
                  />
                  <Input
                    label="Maximum Investment (Optional)"
                    value={formData.maximumInvestment}
                    onChange={(e) => handleInputChange('maximumInvestment', e.target.value)}
                    placeholder="500000"
                    type="number"
                    helperText="Maximum amount per investor"
                  />
                </div>

                <Input
                  label="Funding Deadline"
                  value={formData.fundingDeadline}
                  onChange={(e) => handleInputChange('fundingDeadline', e.target.value)}
                  type="date"
                  leftIcon={<Calendar className="h-4 w-4" />}
                  helperText="Last date to accept investments"
                />

                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div className="text-sm text-yellow-800">
                      <p className="font-medium mb-1">Tokenomics Structure</p>
                      <p>The token structure determines how investors can participate. Ensure the minimum investment is accessible while the token price allows for meaningful ownership fractions.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 5 && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Images
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 mb-2">Upload property images</p>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => handleFileUpload('images', e.target.files)}
                      className="hidden"
                      id="images-upload"
                    />
                    <label htmlFor="images-upload" className="cursor-pointer">
                      <Button variant="outline" size="sm">
                        Choose Files
                      </Button>
                    </label>
                  </div>
                  {formData.images.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <p className="text-sm font-medium text-gray-700">{formData.images.length} images selected:</p>
                      <div className="space-y-2">
                        {formData.images.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm text-gray-600">{file.name}</span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFile('images', index)}
                              className="text-red-600 hover:text-red-800 text-sm"
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Legal Documents
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 mb-2">Upload legal documents</p>
                    <input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx"
                      onChange={(e) => handleFileUpload('documents', e.target.files)}
                      className="hidden"
                      id="documents-upload"
                    />
                    <label htmlFor="documents-upload" className="cursor-pointer">
                      <Button variant="outline" size="sm">
                        Choose Files
                      </Button>
                    </label>
                  </div>
                  {formData.documents.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <p className="text-sm font-medium text-gray-700">{formData.documents.length} documents selected:</p>
                      <div className="space-y-2">
                        {formData.documents.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm text-gray-600">{file.name}</span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFile('documents', index)}
                              className="text-red-600 hover:text-red-800 text-sm"
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div className="text-sm text-green-800">
                      <p className="font-medium mb-1">Required Documents</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Title deed or ownership documents</li>
                        <li>RERA registration certificate</li>
                        <li>Environmental clearances (if applicable)</li>
                        <li>Financial projections and valuations</li>
                        <li>Legal compliance certificates</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 6 && (
              <div className="space-y-6">
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Ready to Submit</h3>
                  <p className="text-gray-600">
                    Please review all information before submitting your property for review.
                  </p>
                </div>

                <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Property:</span>
                      <span className="ml-2 font-medium">{formData.title}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Type:</span>
                      <span className="ml-2 font-medium capitalize">{formData.type}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Location:</span>
                      <span className="ml-2 font-medium">{formData.city}, {formData.state}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Total Value:</span>
                      <span className="ml-2 font-medium">{formData.currency} {formData.totalValue}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Token Price:</span>
                      <span className="ml-2 font-medium">{formData.currency} {formData.tokenPrice}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Expected ROI:</span>
                      <span className="ml-2 font-medium">{formData.expectedROI}%</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium mb-1">What happens next?</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Your property will be reviewed by our team</li>
                        <li>Legal and financial verification will be conducted</li>
                        <li>You&apos;ll receive updates on the review status</li>
                        <li>Once approved, your property will go live for funding</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                Previous
              </Button>
              
              {currentStep < steps.length ? (
                <Button
                  variant="primary"
                  onClick={handleNext}
                  disabled={!validateStep(currentStep)}
                >
                  Next
                </Button>
              ) : (
                <Button
                  variant="primary"
                  onClick={handleSubmit}
                >
                  Submit for Review
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(AddPropertyPage, ['developer'])
