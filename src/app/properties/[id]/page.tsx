'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getMarketDataByProperty } from '@/data/mockMarketData'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatPercentage } from '@/lib/utils'
import {
    ArrowLeft,
    Bath,
    Bed,
    Building2,
    Calculator,
    Calendar,
    Car,
    Download,
    FileText,
    Heart,
    MapPin,
    Ruler,
    Share,
    Star,
    TrendingUp,
    Users
} from 'lucide-react'
import Image from 'next/image'
import { useParams, useRouter } from 'next/navigation'
import { useState } from 'react'

function PropertyDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState<'overview' | 'financials' | 'documents' | 'timeline'>('overview')
  const [investmentAmount, setInvestmentAmount] = useState('')

  const property = mockProperties.find(p => p.id === params.id)
  const marketData = property ? getMarketDataByProperty(property.id) : null

  if (!property) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Property not found</h2>
          <p className="text-gray-600 mb-4">The property you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/properties')}>
            Back to Properties
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  const canInvest = user?.kycStatus === 'verified' && 
    property.status === 'active_funding' &&
    property.tokenomics.availableTokens > 0

  const calculateTokens = (amount: string) => {
    const numAmount = parseFloat(amount)
    if (isNaN(numAmount) || numAmount <= 0) return 0
    return Math.floor(numAmount / property.tokenomics.tokenPrice)
  }

  const formatStatus = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active_funding': return 'info'
      case 'fully_funded': return 'success'
      case 'completed': return 'success'
      case 'under_review': return 'warning'
      default: return 'default'
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'financials', label: 'Financials' },
    { id: 'documents', label: 'Documents' },
    { id: 'timeline', label: 'Timeline' }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => router.push('/properties')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Properties
          </Button>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Heart className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Share className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Property Hero */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Property Image and Basic Info */}
            <Card>
              <div className="relative h-64 overflow-hidden rounded-t-lg">
                <Image
                  src={property.images[0]}
                  alt={property.title}
                  fill
                  className="object-cover"
                />
                <div className="hidden absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-t-lg"></div>
                <div className="absolute inset-0 bg-black bg-opacity-20 rounded-t-lg"></div>
                <div className="absolute top-4 left-4">
                  <Badge variant={getStatusVariant(property.status)}>
                    {formatStatus(property.status)}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary" className="bg-white bg-opacity-90 text-gray-800">
                    {property.type === 'residential' ? 'Residential' : 'Commercial'}
                  </Badge>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">{property.title}</h1>
                    <p className="text-gray-600 flex items-center">
                      <MapPin className="h-4 w-4 mr-2" />
                      {property.location.address}, {property.location.city}, {property.location.country}
                    </p>
                  </div>

                  <p className="text-gray-700">{property.description}</p>

                  {/* Property Metrics */}
                  {property.metrics && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                      <div className="text-center">
                        <Ruler className="h-5 w-5 text-gray-600 mx-auto mb-1" />
                        <div className="text-sm font-medium text-gray-900">
                          {property.metrics.size.toLocaleString()} {property.metrics.sizeUnit}
                        </div>
                        <div className="text-xs text-gray-500">Size</div>
                      </div>
                      {property.metrics.bedrooms && (
                        <div className="text-center">
                          <Bed className="h-5 w-5 text-gray-600 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{property.metrics.bedrooms}</div>
                          <div className="text-xs text-gray-500">Bedrooms</div>
                        </div>
                      )}
                      {property.metrics.bathrooms && (
                        <div className="text-center">
                          <Bath className="h-5 w-5 text-gray-600 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{property.metrics.bathrooms}</div>
                          <div className="text-xs text-gray-500">Bathrooms</div>
                        </div>
                      )}
                      {property.metrics.parking && (
                        <div className="text-center">
                          <Car className="h-5 w-5 text-gray-600 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{property.metrics.parking}</div>
                          <div className="text-xs text-gray-500">Parking</div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Card>
              <CardHeader>
                <div className="flex space-x-1 border-b border-gray-200">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as 'overview' | 'financials' | 'documents' | 'timeline')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    {/* Key Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <TrendingUp className="h-6 w-6 text-green-600 mx-auto mb-2" />
                        <div className="text-lg font-bold text-green-600">
                          {formatPercentage(property.financials.expectedROI)}
                        </div>
                        <div className="text-sm text-gray-600">Expected ROI</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <Users className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                        <div className="text-lg font-bold text-blue-600">
                          {formatPercentage(property.tokenomics.fundingProgress)}
                        </div>
                        <div className="text-sm text-gray-600">Funded</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <Calendar className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                        <div className="text-lg font-bold text-purple-600">
                          {formatPercentage(property.financials.rentalYield)}
                        </div>
                        <div className="text-sm text-gray-600">Rental Yield</div>
                      </div>
                    </div>

                    {/* Developer Info */}
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-3">Developer</h3>
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold">
                            {property.developer.name.charAt(0)}
                          </span>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{property.developer.name}</h4>
                          <p className="text-sm text-gray-600 mb-2">{property.developer.description}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>Est. {property.developer.establishedYear}</span>
                            <span>{property.developer.completedProjects} Projects Completed</span>
                            <span>{property.developer.successRate}% Success Rate</span>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-400 mr-1" />
                              {property.developer.rating}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Nearby Amenities */}
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">Nearby Amenities</h3>
                      <div className="flex flex-wrap gap-2">
                        {property.location.nearbyAmenities.map((amenity, index) => (
                          <Badge key={index} variant="secondary">
                            {amenity}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'financials' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="font-semibold text-gray-900">Investment Details</h3>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Total Property Value</span>
                            <span className="font-semibold financial-data">
                              {formatCompactCurrency(property.financials.totalValue, property.financials.currency)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Token Price</span>
                            <span className="font-semibold financial-data">
                              {formatCompactCurrency(property.tokenomics.tokenPrice, property.financials.currency)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Total Tokens</span>
                            <span className="font-semibold">{property.tokenomics.totalTokens.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Available Tokens</span>
                            <span className="font-semibold">{property.tokenomics.availableTokens.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-semibold text-gray-900">Returns & Fees</h3>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Expected ROI</span>
                            <span className="font-semibold text-green-600">
                              {formatPercentage(property.financials.expectedROI)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Rental Yield</span>
                            <span className="font-semibold text-green-600">
                              {formatPercentage(property.financials.rentalYield)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Management Fee</span>
                            <span className="font-semibold">{formatPercentage(property.financials.managementFee)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Platform Fee</span>
                            <span className="font-semibold">{formatPercentage(property.financials.platformFee)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'documents' && (
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900">Legal Documents</h3>
                    <div className="space-y-3">
                      {property.documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-5 w-5 text-gray-400" />
                            <div>
                              <div className="font-medium text-gray-900">{doc.name}</div>
                              <div className="text-sm text-gray-500 capitalize">{doc.type}</div>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === 'timeline' && (
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900">Project Timeline</h3>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <div>
                          <div className="font-medium">Property Listed</div>
                          <div className="text-sm text-gray-500">
                            {new Date(property.timeline.listingDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <div>
                          <div className="font-medium">Funding Period</div>
                          <div className="text-sm text-gray-500">
                            {new Date(property.timeline.fundingStartDate).toLocaleDateString()} - {new Date(property.timeline.fundingEndDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      {property.timeline.expectedCompletionDate && (
                        <div className="flex items-center space-x-4">
                          <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                          <div>
                            <div className="font-medium">Expected Completion</div>
                            <div className="text-sm text-gray-500">
                              {new Date(property.timeline.expectedCompletionDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Investment Sidebar */}
          <div className="space-y-6">
            {/* Investment Calculator */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calculator className="h-5 w-5 mr-2" />
                  Investment Calculator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Investment Amount ({property.financials.currency})
                  </label>
                  <input
                    type="number"
                    value={investmentAmount}
                    onChange={(e) => setInvestmentAmount(e.target.value)}
                    placeholder={`Min: ${property.tokenomics.minimumInvestment}`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {investmentAmount && (
                  <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between text-sm">
                      <span>Tokens</span>
                      <span className="font-medium">{calculateTokens(investmentAmount)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Est. Annual Return</span>
                      <span className="font-medium text-green-600">
                        {formatCompactCurrency(
                          (parseFloat(investmentAmount) || 0) * (property.financials.expectedROI / 100),
                          property.financials.currency
                        )}
                      </span>
                    </div>
                  </div>
                )}

                <Button
                  variant="primary"
                  size="lg"
                  className="w-full"
                  disabled={!canInvest}
                  onClick={() => router.push(`/properties/${property.id}/invest`)}
                >
                  {canInvest ? 'Invest Now' : 
                   user?.kycStatus !== 'verified' ? 'Complete KYC to Invest' :
                   property.status !== 'active_funding' ? 'Funding Closed' :
                   'Fully Funded'}
                </Button>

                <div className="text-xs text-gray-500 text-center">
                  Minimum investment: {formatCompactCurrency(property.tokenomics.minimumInvestment, property.financials.currency)}
                </div>
              </CardContent>
            </Card>

            {/* Funding Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Funding Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span className="font-medium">{formatPercentage(property.tokenomics.fundingProgress)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(property.tokenomics.fundingProgress, 100)}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Raised</span>
                    <span className="font-medium financial-data">
                      {formatCompactCurrency(
                        property.tokenomics.fundingTarget * (property.tokenomics.fundingProgress / 100),
                        property.financials.currency
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Target</span>
                    <span className="font-medium financial-data">
                      {formatCompactCurrency(property.tokenomics.fundingTarget, property.financials.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Investors</span>
                    <span className="font-medium">{property.tokenomics.soldTokens}</span>
                  </div>
                </div>

                {property.timeline.fundingEndDate && (
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>
                        Ends: {new Date(property.timeline.fundingEndDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Market Data */}
            {marketData && (
              <Card>
                <CardHeader>
                  <CardTitle>Market Performance</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Current Price</span>
                    <span className="font-semibold financial-data">
                      {formatCompactCurrency(marketData.currentPrice, property.financials.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">24h Change</span>
                    <span className={`font-semibold ${marketData.priceChangePercent24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {marketData.priceChangePercent24h >= 0 ? '+' : ''}{formatPercentage(marketData.priceChangePercent24h)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">24h Volume</span>
                    <span className="font-semibold">{marketData.volume24h} tokens</span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(PropertyDetailsPage, ['retail_investor', 'institutional_investor', 'developer', 'admin'])
