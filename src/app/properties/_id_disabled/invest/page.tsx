'use client'

import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { mockProperties } from '@/data/mockProperties'
import { calculateAmountFromTokens, calculateTokensFromAmount, formatCompactCurrency, formatPercentage } from '@/lib/utils'
import {
    AlertCircle,
    ArrowLeft,
    Building2,
    Calculator,
    CheckCircle,
    Clock,
    CreditCard,
    Info,
    Shield,
    Smartphone
} from 'lucide-react'
import Image from 'next/image'
import { useParams, useRouter } from 'next/navigation'
import { useState } from 'react'

function InvestPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [step, setStep] = useState(1)
  const [investmentAmount, setInvestmentAmount] = useState('')
  const [paymentMethod, setPaymentMethod] = useState<'upi' | 'bank_transfer' | 'card'>('upi')
  const [isProcessing, setIsProcessing] = useState(false)

  const property = mockProperties.find(p => p.id === params.id)

  if (!property) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Property not found</h2>
          <p className="text-gray-600 mb-4">The property you&apos;re trying to invest in doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/properties')}>
            Back to Properties
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  const canInvest = user?.kycStatus === 'verified' && 
    property.status === 'active_funding' &&
    property.tokenomics.availableTokens > 0

  if (!canInvest) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Investment Not Available</h2>
          <p className="text-gray-600 mb-4">
            {user?.kycStatus !== 'verified' ? 'Please complete your KYC verification to invest.' :
             property.status !== 'active_funding' ? 'This property is not currently accepting investments.' :
             'This property is fully funded.'}
          </p>
          <Button onClick={() => router.push(`/properties/${property.id}`)}>
            Back to Property
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  const numAmount = parseFloat(investmentAmount) || 0
  const tokens = calculateTokensFromAmount(numAmount, property.tokenomics.tokenPrice)
  const actualAmount = calculateAmountFromTokens(tokens, property.tokenomics.tokenPrice)
  const estimatedAnnualReturn = actualAmount * (property.financials.expectedROI / 100)
  const estimatedMonthlyReturn = estimatedAnnualReturn / 12

  const isValidAmount = numAmount >= property.tokenomics.minimumInvestment && 
    tokens <= property.tokenomics.availableTokens &&
    (!property.tokenomics.maximumInvestment || numAmount <= property.tokenomics.maximumInvestment)

  const handleInvest = async () => {
    if (!isValidAmount) return

    setIsProcessing(true)
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 3000))
    setIsProcessing(false)
    setStep(4) // Success step
  }

  const paymentMethods = [
    {
      id: 'upi' as const,
      name: 'UPI',
      description: 'Instant payment via UPI',
      icon: Smartphone,
      processingTime: 'Instant'
    },
    {
      id: 'bank_transfer' as const,
      name: 'Bank Transfer',
      description: 'Direct bank transfer',
      icon: Building2,
      processingTime: '1-2 hours'
    },
    {
      id: 'card' as const,
      name: 'Debit/Credit Card',
      description: 'Pay with your card',
      icon: CreditCard,
      processingTime: 'Instant'
    }
  ]

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => router.push(`/properties/${property.id}`)}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Property
          </Button>
          <div className="flex items-center space-x-2">
            {[1, 2, 3, 4].map((stepNum) => (
              <div
                key={stepNum}
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  stepNum <= step
                    ? 'bg-blue-600 text-white'
                    : stepNum === step + 1
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-400'
                }`}
              >
                {stepNum < step ? <CheckCircle className="h-4 w-4" /> : stepNum}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {step === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calculator className="h-5 w-5 mr-2" />
                    Investment Amount
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Input
                      label={`Investment Amount (${property.financials.currency})`}
                      type="number"
                      value={investmentAmount}
                      onChange={(e) => setInvestmentAmount(e.target.value)}
                      placeholder={`Minimum: ${formatCompactCurrency(property.tokenomics.minimumInvestment, property.financials.currency)}`}
                      error={investmentAmount && !isValidAmount ? 'Invalid investment amount' : undefined}
                    />
                    <div className="mt-2 text-sm text-gray-500">
                      Min: {formatCompactCurrency(property.tokenomics.minimumInvestment, property.financials.currency)}
                      {property.tokenomics.maximumInvestment && (
                        <> • Max: {formatCompactCurrency(property.tokenomics.maximumInvestment, property.financials.currency)}</>
                      )}
                    </div>
                  </div>

                  {investmentAmount && (
                    <div className="space-y-4 p-4 bg-blue-50 rounded-lg">
                      <h3 className="font-semibold text-gray-900">Investment Summary</h3>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Tokens</div>
                          <div className="font-semibold">{tokens.toLocaleString()}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Actual Amount</div>
                          <div className="font-semibold financial-data">
                            {formatCompactCurrency(actualAmount, property.financials.currency)}
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">Est. Annual Return</div>
                          <div className="font-semibold text-green-600 financial-data">
                            {formatCompactCurrency(estimatedAnnualReturn, property.financials.currency)}
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">Est. Monthly Return</div>
                          <div className="font-semibold text-green-600 financial-data">
                            {formatCompactCurrency(estimatedMonthlyReturn, property.financials.currency)}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    disabled={!isValidAmount}
                    onClick={() => setStep(2)}
                  >
                    Continue to Payment
                  </Button>
                </CardContent>
              </Card>
            )}

            {step === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle>Select Payment Method</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {paymentMethods.map((method) => {
                    const Icon = method.icon
                    return (
                      <button
                        key={method.id}
                        onClick={() => setPaymentMethod(method.id)}
                        className={`w-full p-4 border-2 rounded-lg text-left transition-all ${
                          paymentMethod === method.id
                            ? 'border-blue-600 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Icon className="h-6 w-6 text-gray-600" />
                            <div>
                              <div className="font-medium text-gray-900">{method.name}</div>
                              <div className="text-sm text-gray-500">{method.description}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-500">Processing Time</div>
                            <div className="text-sm font-medium">{method.processingTime}</div>
                          </div>
                        </div>
                      </button>
                    )
                  })}

                  <div className="flex space-x-3 mt-6">
                    <Button variant="outline" onClick={() => setStep(1)} className="flex-1">
                      Back
                    </Button>
                    <Button variant="primary" onClick={() => setStep(3)} className="flex-1">
                      Continue
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {step === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle>Confirm Investment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="p-4 bg-gray-50 rounded-lg space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Property</span>
                      <span className="font-medium">{property.title}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Investment Amount</span>
                      <span className="font-medium financial-data">
                        {formatCompactCurrency(actualAmount, property.financials.currency)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tokens</span>
                      <span className="font-medium">{tokens.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payment Method</span>
                      <span className="font-medium">
                        {paymentMethods.find(m => m.id === paymentMethod)?.name}
                      </span>
                    </div>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">Important Information</p>
                        <ul className="space-y-1 text-blue-700">
                          <li>• Your investment will be processed within the specified timeframe</li>
                          <li>• You will receive tokens representing your ownership share</li>
                          <li>• Returns are distributed quarterly based on property performance</li>
                          <li>• You can trade your tokens on the secondary market</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <Button variant="outline" onClick={() => setStep(2)} className="flex-1">
                      Back
                    </Button>
                    <Button 
                      variant="primary" 
                      onClick={handleInvest} 
                      className="flex-1"
                      isLoading={isProcessing}
                    >
                      {isProcessing ? 'Processing...' : 'Confirm Investment'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {step === 4 && (
              <Card>
                <CardContent className="text-center py-12">
                  <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-6" />
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Investment Successful!</h2>
                  <p className="text-gray-600 mb-8">
                    Your investment of {formatCompactCurrency(actualAmount, property.financials.currency)} has been processed successfully.
                    You now own {tokens.toLocaleString()} tokens of {property.title}.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button variant="primary" onClick={() => router.push('/portfolio')}>
                      View Portfolio
                    </Button>
                    <Button variant="outline" onClick={() => router.push('/properties')}>
                      Browse More Properties
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Property Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Property Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative h-32 overflow-hidden rounded-lg">
                  <Image
                    src={property.images[0]}
                    alt={property.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{property.title}</h3>
                  <p className="text-sm text-gray-600">{property.location.city}, {property.location.country}</p>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expected ROI</span>
                    <span className="font-medium text-green-600">
                      {formatPercentage(property.financials.expectedROI)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Token Price</span>
                    <span className="font-medium financial-data">
                      {formatCompactCurrency(property.tokenomics.tokenPrice, property.financials.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Available Tokens</span>
                    <span className="font-medium">{property.tokenomics.availableTokens.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Security & Trust
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>SEBI Regulated Platform</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Blockchain-based Ownership</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Legal Documentation</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Transparent Returns</span>
                </div>
              </CardContent>
            </Card>

            {/* Investment Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  What Happens Next?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium">Payment Processing</div>
                    <div className="text-gray-600">Your payment will be processed within the selected timeframe</div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium">Token Allocation</div>
                    <div className="text-gray-600">Tokens will be allocated to your portfolio</div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium">Return Distribution</div>
                    <div className="text-gray-600">Quarterly returns based on property performance</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(InvestPage, ['retail_investor', 'institutional_investor'])
