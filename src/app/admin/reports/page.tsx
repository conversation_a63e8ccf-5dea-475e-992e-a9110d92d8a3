'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { mockPlatformAnalytics } from '@/data/mockMarketData'
import { formatCompactCurrency, formatDate } from '@/lib/utils'
import {
    Activity,
    BarChart3,
    Building2,
    CheckCircle,
    DollarSign,
    Download,
    Eye,
    FileText,
    TrendingUp,
    Users
} from 'lucide-react'
import { useState } from 'react'

function AdminReportsPage() {
  const { user } = useAuth()
  const [selectedReport, setSelectedReport] = useState<string>('financial')
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  if (!user) return null

  const analytics = mockPlatformAnalytics

  const reportTypes = [
    { id: 'financial', name: 'Financial Report', icon: DollarSign, description: 'Revenue, investments, and financial metrics' },
    { id: 'user', name: 'User Analytics', icon: Users, description: 'User growth, engagement, and demographics' },
    { id: 'property', name: 'Property Report', icon: Building2, description: 'Property performance and listings' },
    { id: 'compliance', name: 'Compliance Report', icon: CheckCircle, description: 'KYC, legal, and regulatory compliance' },
    { id: 'performance', name: 'Performance Report', icon: TrendingUp, description: 'Platform performance and ROI metrics' },
    { id: 'activity', name: 'Activity Report', icon: Activity, description: 'User activity and transaction logs' }
  ]

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' }
  ]

  // Mock report data
  const getReportData = (reportType: string) => {
    switch (reportType) {
      case 'financial':
        return {
          title: 'Financial Performance Report',
          summary: 'Comprehensive financial overview of platform performance',
          metrics: [
            { label: 'Total Revenue', value: formatCompactCurrency(5200000, 'INR'), change: '+15.2%' },
            { label: 'Total Investments', value: formatCompactCurrency(analytics.totalInvestments, 'INR'), change: '+22.8%' },
            { label: 'Platform Fees', value: formatCompactCurrency(780000, 'INR'), change: '+18.5%' },
            { label: 'Distributions Paid', value: formatCompactCurrency(analytics.totalDistributions, 'INR'), change: '+12.1%' }
          ],
          sections: [
            'Revenue Breakdown by Source',
            'Investment Volume Trends',
            'Fee Structure Analysis',
            'Distribution Performance',
            'Profit & Loss Statement'
          ]
        }
      case 'user':
        return {
          title: 'User Analytics Report',
          summary: 'User growth, engagement, and demographic insights',
          metrics: [
            { label: 'Total Users', value: analytics.totalUsers.toLocaleString(), change: '+28.5%' },
            { label: 'Active Users', value: analytics.activeInvestors.toLocaleString(), change: '+15.3%' },
            { label: 'New Registrations', value: '1,247', change: '+32.1%' },
            { label: 'User Retention', value: '78.5%', change: '+5.2%' }
          ],
          sections: [
            'User Growth Trends',
            'Geographic Distribution',
            'User Type Breakdown',
            'Engagement Metrics',
            'Retention Analysis'
          ]
        }
      case 'property':
        return {
          title: 'Property Performance Report',
          summary: 'Property listings, performance, and investment metrics',
          metrics: [
            { label: 'Total Properties', value: analytics.totalProperties.toString(), change: '+12.5%' },
            { label: 'Active Properties', value: analytics.activeProperties.toString(), change: '+8.7%' },
            { label: 'Avg Funding Time', value: '45 days', change: '-15.2%' },
            { label: 'Success Rate', value: '92.3%', change: '+3.1%' }
          ],
          sections: [
            'Property Listing Trends',
            'Funding Performance',
            'Geographic Analysis',
            'Property Type Breakdown',
            'Developer Performance'
          ]
        }
      case 'compliance':
        return {
          title: 'Compliance & Regulatory Report',
          summary: 'KYC verification, legal compliance, and regulatory metrics',
          metrics: [
            { label: 'KYC Verified', value: '94.2%', change: '+2.8%' },
            { label: 'Pending Reviews', value: '127', change: '-18.5%' },
            { label: 'Compliance Score', value: '98.5%', change: '+1.2%' },
            { label: 'Legal Issues', value: '0', change: '0%' }
          ],
          sections: [
            'KYC Verification Status',
            'Regulatory Compliance',
            'Legal Documentation',
            'Risk Assessment',
            'Audit Trail'
          ]
        }
      case 'performance':
        return {
          title: 'Platform Performance Report',
          summary: 'ROI metrics, investment performance, and market analysis',
          metrics: [
            { label: 'Average ROI', value: `${analytics.averageROI.toFixed(1)}%`, change: '+2.1%' },
            { label: 'Best Performing', value: '15.8% ROI', change: '+0.5%' },
            { label: 'Portfolio Growth', value: '18.2%', change: '+3.2%' },
            { label: 'Market Outperformance', value: '+4.5%', change: '+1.1%' }
          ],
          sections: [
            'ROI Performance Analysis',
            'Property Performance Ranking',
            'Market Comparison',
            'Risk-Adjusted Returns',
            'Performance Attribution'
          ]
        }
      case 'activity':
        return {
          title: 'Platform Activity Report',
          summary: 'User activity, transaction logs, and system usage metrics',
          metrics: [
            { label: 'Daily Active Users', value: '2,847', change: '+12.5%' },
            { label: 'Transactions', value: '15,234', change: '+25.8%' },
            { label: 'Page Views', value: '125K', change: '+18.2%' },
            { label: 'Session Duration', value: '8.5 min', change: '+15.3%' }
          ],
          sections: [
            'User Activity Patterns',
            'Transaction Analysis',
            'System Usage Metrics',
            'Feature Adoption',
            'Performance Logs'
          ]
        }
      default:
        return null
    }
  }

  const currentReport = getReportData(selectedReport)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600 mt-1">
              Generate comprehensive reports and analytics
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as '7d' | '30d' | '90d' | '1y')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              {dateRangeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Button variant="primary" leftIcon={<Download className="h-4 w-4" />}>
              Export All Reports
            </Button>
          </div>
        </div>

        {/* Report Types */}
        <Card>
          <CardHeader>
            <CardTitle>Available Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {reportTypes.map((report) => {
                const Icon = report.icon
                return (
                  <button
                    key={report.id}
                    onClick={() => setSelectedReport(report.id)}
                    className={`p-4 border-2 rounded-lg text-left transition-all hover:shadow-md ${
                      selectedReport === report.id
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <Icon className={`h-6 w-6 ${
                        selectedReport === report.id ? 'text-blue-600' : 'text-gray-600'
                      }`} />
                      <h3 className="font-semibold text-gray-900">{report.name}</h3>
                    </div>
                    <p className="text-sm text-gray-600">{report.description}</p>
                  </button>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Selected Report */}
        {currentReport && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{currentReport.title}</CardTitle>
                  <p className="text-gray-600 mt-1">{currentReport.summary}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="info">
                    {dateRangeOptions.find(d => d.value === dateRange)?.label}
                  </Badge>
                  <Button variant="outline" leftIcon={<Download className="h-4 w-4" />}>
                    Export PDF
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {currentReport.metrics.map((metric, index) => (
                  <div key={index} className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600 mb-1">{metric.label}</div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
                    <div className={`text-sm font-medium ${
                      metric.change.startsWith('+') ? 'text-green-600' : 
                      metric.change.startsWith('-') ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {metric.change} vs previous period
                    </div>
                  </div>
                ))}
              </div>

              {/* Report Sections */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Report Sections</h3>
                <div className="space-y-3">
                  {currentReport.sections.map((section, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 text-sm font-semibold">{index + 1}</span>
                        </div>
                        <span className="font-medium text-gray-900">{section}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="success" size="sm">Ready</Badge>
                        <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />}>
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Chart Placeholder */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Visual Analytics</h3>
                <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Interactive charts and visualizations</p>
                    <p className="text-sm text-gray-500 mt-2">
                      Detailed analytics for {currentReport.title.toLowerCase()}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Reports */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { name: 'Monthly Financial Report - June 2024', type: 'Financial', date: '2024-07-01', status: 'completed' },
                { name: 'Q2 User Analytics Report', type: 'User Analytics', date: '2024-06-30', status: 'completed' },
                { name: 'Property Performance Review', type: 'Property', date: '2024-06-28', status: 'completed' },
                { name: 'Compliance Audit Report', type: 'Compliance', date: '2024-06-25', status: 'completed' },
                { name: 'Weekly Activity Summary', type: 'Activity', date: '2024-06-23', status: 'completed' }
              ].map((report, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <FileText className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{report.name}</div>
                      <div className="text-sm text-gray-500">
                        {report.type} • Generated {formatDate(report.date)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="success" size="sm">
                      {report.status}
                    </Badge>
                    <Button variant="outline" size="sm" leftIcon={<Download className="h-4 w-4" />}>
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(AdminReportsPage, ['admin'])
