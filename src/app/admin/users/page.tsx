'use client'

import { Badge } from '@/components/ui/Badge'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getPendingKYCUsers, getVerifiedUsers, mockUsers } from '@/data/mockUsers'
import { formatDate } from '@/lib/utils'
import {
    Download,
    Edit,
    Eye,
    Filter,
    MoreVertical,
    Search,
    Shield,
    ShieldCheck,
    ShieldX,
    UserCheck,
    Users,
    UserX
} from 'lucide-react'
import { useState } from 'react'

function AdminUsersPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [userTypeFilter, setUserTypeFilter] = useState<string>('all')
  const [kycStatusFilter, setKycStatusFilter] = useState<string>('all')
  const [activeTab, setActiveTab] = useState<'all' | 'pending_kyc' | 'verified' | 'inactive'>('all')

  if (!user) return null

  const allUsers = mockUsers.filter(u => u.id !== user.id) // Exclude current admin user
  const verifiedUsers = getVerifiedUsers().filter(u => u.id !== user.id)
  const pendingKYCUsers = getPendingKYCUsers()
  const inactiveUsers = allUsers.filter(u => !u.isActive)

  const filteredUsers = allUsers.filter(user => {
    const matchesSearch = searchQuery === '' ||
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesUserType = userTypeFilter === 'all' || user.userType === userTypeFilter
    const matchesKYCStatus = kycStatusFilter === 'all' || user.kycStatus === kycStatusFilter

    const matchesTab = activeTab === 'all' ||
      (activeTab === 'pending_kyc' && user.kycStatus === 'pending') ||
      (activeTab === 'verified' && user.kycStatus === 'verified') ||
      (activeTab === 'inactive' && !user.isActive)

    return matchesSearch && matchesUserType && matchesKYCStatus && matchesTab
  })

  // Action handlers
  const handleViewUser = (userId: string) => {
    alert(`View User Details for: ${userId}`)
  }

  const handleEditUser = (userId: string) => {
    alert(`Edit User for: ${userId}`)
  }



  const userTypeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'retail_investor', label: 'Retail Investor' },
    { value: 'institutional_investor', label: 'Institutional Investor' },
    { value: 'developer', label: 'Developer' },
    { value: 'admin', label: 'Admin' }
  ]

  const kycStatusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'verified', label: 'Verified' },
    { value: 'rejected', label: 'Rejected' }
  ]

  const tabs = [
    { id: 'all', label: 'All Users', count: allUsers.length },
    { id: 'pending_kyc', label: 'Pending KYC', count: pendingKYCUsers.length },
    { id: 'verified', label: 'Verified', count: verifiedUsers.length },
    { id: 'inactive', label: 'Inactive', count: inactiveUsers.length }
  ]

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'retail_investor': return 'Retail Investor'
      case 'institutional_investor': return 'Institutional Investor'
      case 'developer': return 'Developer'
      case 'admin': return 'Admin'
      default: return 'User'
    }
  }

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'retail_investor': return 'info'
      case 'institutional_investor': return 'secondary'
      case 'developer': return 'success'
      case 'admin': return 'warning'
      default: return 'default'
    }
  }

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'success'
      case 'pending': return 'warning'
      case 'rejected': return 'error'
      default: return 'default'
    }
  }

  const getKYCStatusIcon = (status: string) => {
    switch (status) {
      case 'verified': return ShieldCheck
      case 'pending': return Shield
      case 'rejected': return ShieldX
      default: return Shield
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600 mt-1">
              Manage platform users and KYC verification
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <Button variant="outline" leftIcon={<Download className="h-4 w-4" />}>
              Export Users
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{allUsers.length}</div>
              <div className="text-sm text-gray-600">Total Users</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <UserCheck className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{verifiedUsers.length}</div>
              <div className="text-sm text-gray-600">Verified Users</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Shield className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{pendingKYCUsers.length}</div>
              <div className="text-sm text-gray-600">Pending KYC</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <UserX className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{inactiveUsers.length}</div>
              <div className="text-sm text-gray-600">Inactive Users</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search users by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={userTypeFilter}
                  onChange={(e) => setUserTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  {userTypeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <select
                  value={kycStatusFilter}
                  onChange={(e) => setKycStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  {kycStatusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 border-b border-gray-200">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'verified' | 'inactive' | 'all' | 'pending_kyc')}
                  className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                  <span className="ml-2 px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                    {tab.count}
                  </span>
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">User</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">KYC Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Joined</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Last Login</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => {
                    const KYCIcon = getKYCStatusIcon(user.kycStatus)
                    
                    return (
                      <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                              <span className="text-white text-sm font-semibold">
                                {user.name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge variant={getUserTypeColor(user.userType)}>
                            {getUserTypeLabel(user.userType)}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <KYCIcon className="h-4 w-4" />
                            <Badge variant={getKYCStatusColor(user.kycStatus)}>
                              {user.kycStatus.charAt(0).toUpperCase() + user.kycStatus.slice(1)}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge variant={user.isActive ? 'success' : 'error'}>
                            {user.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm text-gray-600">
                            {formatDate(user.createdAt)}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm text-gray-600">
                            {formatDate(user.lastLogin)}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />} onClick={() => handleViewUser(user.id)}>
                              View
                            </Button>
                            <Button variant="outline" size="sm" leftIcon={<Edit className="h-4 w-4" />} onClick={() => handleEditUser(user.id)}>
                              Edit
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(AdminUsersPage, ['admin'])
