'use client'

import { Badge } from '@/components/ui/Badge'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getPendingKYCUsers, getVerifiedUsers, mockUsers } from '@/data/mockUsers'
import { formatDate } from '@/lib/utils'
import {
    AlertTriangle,
    CheckCircle,
    Clock,
    Download,
    Eye,
    FileText,
    Filter,
    Search,
    Shield,
    ShieldCheck,
    ShieldX,
    XCircle
} from 'lucide-react'
import { useState } from 'react'

function AdminKYCPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [userTypeFilter, setUserTypeFilter] = useState<string>('all')
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'verified' | 'rejected'>('pending')

  if (!user) return null

  const allKYCUsers = mockUsers.filter(u => u.id !== user.id) // Exclude current admin
  const pendingKYCUsers = getPendingKYCUsers()
  const verifiedUsers = getVerifiedUsers().filter(u => u.id !== user.id)
  const rejectedUsers = allKYCUsers.filter(u => u.kycStatus === 'rejected')

  const filteredUsers = allKYCUsers.filter(kycUser => {
    const matchesSearch = searchQuery === '' ||
      kycUser.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      kycUser.email.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === 'all' || kycUser.kycStatus === statusFilter
    const matchesUserType = userTypeFilter === 'all' || kycUser.userType === userTypeFilter

    const matchesTab = activeTab === 'all' ||
      (activeTab === 'pending' && kycUser.kycStatus === 'pending') ||
      (activeTab === 'verified' && kycUser.kycStatus === 'verified') ||
      (activeTab === 'rejected' && kycUser.kycStatus === 'rejected')

    return matchesSearch && matchesStatus && matchesUserType && matchesTab
  })

  // Action handlers
  const handleReviewDocuments = (userId: string) => {
    alert(`Review Documents for user: ${userId}`)
  }

  const handleApprove = (userId: string) => {
    if (confirm('Are you sure you want to approve this KYC application?')) {
      alert(`KYC Application for user ${userId} approved successfully!`)
    }
  }

  const handleReject = (userId: string) => {
    const reason = prompt('Please provide a reason for rejection:')
    if (reason) {
      alert(`KYC Application for user ${userId} rejected. Reason: ${reason}`)
    }
  }

  // const handleDownloadDocument = (documentName: string) => {
  //   alert(`Download document: ${documentName}`)
  // }

  const tabs = [
    { id: 'all', label: 'All KYC', count: allKYCUsers.length },
    { id: 'pending', label: 'Pending Review', count: pendingKYCUsers.length },
    { id: 'verified', label: 'Verified', count: verifiedUsers.length },
    { id: 'rejected', label: 'Rejected', count: rejectedUsers.length }
  ]

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'verified', label: 'Verified' },
    { value: 'rejected', label: 'Rejected' }
  ]

  const userTypeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'retail_investor', label: 'Retail Investor' },
    { value: 'institutional_investor', label: 'Institutional Investor' },
    { value: 'developer', label: 'Developer' }
  ]

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'success'
      case 'pending': return 'warning'
      case 'rejected': return 'error'
      default: return 'default'
    }
  }

  const getKYCStatusIcon = (status: string) => {
    switch (status) {
      case 'verified': return ShieldCheck
      case 'pending': return Shield
      case 'rejected': return ShieldX
      default: return Shield
    }
  }

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'retail_investor': return 'Retail Investor'
      case 'institutional_investor': return 'Institutional Investor'
      case 'developer': return 'Developer'
      case 'admin': return 'Admin'
      default: return 'User'
    }
  }

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'retail_investor': return 'info'
      case 'institutional_investor': return 'secondary'
      case 'developer': return 'success'
      case 'admin': return 'warning'
      default: return 'default'
    }
  }

  // Mock KYC documents for demonstration
  const getKYCDocuments = () => [
    { type: 'Identity Proof', name: 'Aadhaar Card', status: 'submitted' },
    { type: 'Address Proof', name: 'Utility Bill', status: 'submitted' },
    { type: 'Income Proof', name: 'Salary Slip', status: 'submitted' },
    { type: 'Bank Statement', name: 'Account Statement', status: 'submitted' }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">KYC Review</h1>
            <p className="text-gray-600 mt-1">
              Review and verify user KYC submissions
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <Button variant="outline" leftIcon={<Download className="h-4 w-4" />}>
              Export Report
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{allKYCUsers.length}</div>
              <div className="text-sm text-gray-600">Total KYC</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{pendingKYCUsers.length}</div>
              <div className="text-sm text-gray-600">Pending Review</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <ShieldCheck className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{verifiedUsers.length}</div>
              <div className="text-sm text-gray-600">Verified</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <ShieldX className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{rejectedUsers.length}</div>
              <div className="text-sm text-gray-600">Rejected</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search users by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <select
                  value={userTypeFilter}
                  onChange={(e) => setUserTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  {userTypeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 border-b border-gray-200">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'pending' | 'verified' | 'rejected' | 'all')}
                  className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                  <span className="ml-2 px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                    {tab.count}
                  </span>
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredUsers.map((kycUser) => {
                const KYCIcon = getKYCStatusIcon(kycUser.kycStatus)
                const documents = getKYCDocuments()
                
                return (
                  <div key={kycUser.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-lg font-semibold">
                            {kycUser.name.charAt(0)}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{kycUser.name}</h3>
                              <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                                <span>{kycUser.email}</span>
                                <span>•</span>
                                <Badge variant={getUserTypeColor(kycUser.userType)}>
                                  {getUserTypeLabel(kycUser.userType)}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <KYCIcon className="h-5 w-5" />
                              <Badge variant={getKYCStatusColor(kycUser.kycStatus)}>
                                {kycUser.kycStatus.charAt(0).toUpperCase() + kycUser.kycStatus.slice(1)}
                              </Badge>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                            <div>
                              <div className="text-sm text-gray-600">Submitted</div>
                              <div className="font-semibold">
                                {formatDate(kycUser.createdAt)}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Last Login</div>
                              <div className="font-semibold">
                                {formatDate(kycUser.lastLogin)}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Account Status</div>
                              <Badge variant={kycUser.isActive ? 'success' : 'error'} size="sm">
                                {kycUser.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </div>
                          </div>

                          {/* KYC Documents */}
                          <div className="mt-4">
                            <h4 className="text-sm font-medium text-gray-900 mb-2">
                              Submitted Documents
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {documents.map((doc, index) => (
                                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                  <div className="flex items-center space-x-2">
                                    <FileText className="h-4 w-4 text-gray-600" />
                                    <span className="text-sm font-medium">{doc.type}</span>
                                  </div>
                                  <Badge variant="info" size="sm">
                                    {doc.status}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>

                          {kycUser.kycStatus === 'pending' && (
                            <div className="mt-4 p-3 bg-orange-50 rounded-lg">
                              <div className="flex items-center space-x-2">
                                <AlertTriangle className="h-4 w-4 text-orange-600" />
                                <span className="text-sm text-orange-800 font-medium">
                                  Pending Review - Requires admin verification
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2 ml-4">
                        <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />} onClick={() => handleReviewDocuments(kycUser.id)}>
                          Review Documents
                        </Button>
                        {kycUser.kycStatus === 'pending' && (
                          <>
                            <Button variant="primary" size="sm" leftIcon={<CheckCircle className="h-4 w-4" />} onClick={() => handleApprove(kycUser.id)}>
                              Approve
                            </Button>
                            <Button variant="destructive" size="sm" leftIcon={<XCircle className="h-4 w-4" />} onClick={() => handleReject(kycUser.id)}>
                              Reject
                            </Button>
                          </>
                        )}
                        {kycUser.kycStatus === 'rejected' && (
                          <Button variant="outline" size="sm" leftIcon={<Shield className="h-4 w-4" />}>
                            Request Resubmission
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-12">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No KYC submissions found</h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(AdminKYCPage, ['admin'])
