'use client'

import { Badge } from '@/components/ui/Badge'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatDate } from '@/lib/utils'
import {
    AlertTriangle,
    Building2,
    CheckCircle,
    Clock,
    Download,
    Eye,
    Filter,
    MapPin,
    MoreVertical,
    Search,
    User,
    XCircle
} from 'lucide-react'
import { useState } from 'react'

function AdminPropertiesPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')

  if (!user) return null

  const allProperties = mockProperties
  const pendingProperties = allProperties.filter(p => p.status === 'under_review')
  const approvedProperties = allProperties.filter(p => p.status === 'active_funding' || p.status === 'fully_funded' || p.status === 'completed')
  const rejectedProperties = allProperties.filter(p => p.status === 'rejected')

  const filteredProperties = allProperties.filter(property => {
    const matchesSearch = searchQuery === '' || 
      property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      property.location.city.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter
    const matchesType = typeFilter === 'all' || property.type === typeFilter
    
    const matchesTab = activeTab === 'all' || 
      (activeTab === 'pending' && property.status === 'under_review') ||
      (activeTab === 'approved' && ['active_funding', 'fully_funded', 'completed'].includes(property.status)) ||
      (activeTab === 'rejected' && property.status === 'rejected')
    
    return matchesSearch && matchesStatus && matchesType && matchesTab
  })

  const tabs = [
    { id: 'all', label: 'All Properties', count: allProperties.length },
    { id: 'pending', label: 'Pending Review', count: pendingProperties.length },
    { id: 'approved', label: 'Approved', count: approvedProperties.length },
    { id: 'rejected', label: 'Rejected', count: rejectedProperties.length }
  ]

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'under_review', label: 'Under Review' },
    { value: 'active_funding', label: 'Active Funding' },
    { value: 'fully_funded', label: 'Fully Funded' },
    { value: 'completed', label: 'Completed' },
    { value: 'rejected', label: 'Rejected' }
  ]

  const typeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'residential', label: 'Residential' },
    { value: 'commercial', label: 'Commercial' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active_funding': return 'info'
      case 'fully_funded': return 'success'
      case 'completed': return 'success'
      case 'under_review': return 'warning'
      case 'rejected': return 'error'
      default: return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active_funding': return Clock
      case 'fully_funded': return CheckCircle
      case 'completed': return CheckCircle
      case 'under_review': return AlertTriangle
      case 'rejected': return XCircle
      default: return Clock
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active_funding': return 'Active Funding'
      case 'fully_funded': return 'Fully Funded'
      case 'completed': return 'Completed'
      case 'under_review': return 'Under Review'
      case 'rejected': return 'Rejected'
      default: return status
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Property Oversight</h1>
            <p className="text-gray-600 mt-1">
              Review and manage property listings on the platform
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <Button variant="outline" leftIcon={<Download className="h-4 w-4" />}>
              Export Report
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{allProperties.length}</div>
              <div className="text-sm text-gray-600">Total Properties</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{pendingProperties.length}</div>
              <div className="text-sm text-gray-600">Pending Review</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{approvedProperties.length}</div>
              <div className="text-sm text-gray-600">Approved</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{rejectedProperties.length}</div>
              <div className="text-sm text-gray-600">Rejected</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search properties by name or location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  {typeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 border-b border-gray-200">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'pending' | 'rejected' | 'all' | 'approved')}
                  className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                  <span className="ml-2 px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                    {tab.count}
                  </span>
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredProperties.map((property) => {
                const StatusIcon = getStatusIcon(property.status)
                
                return (
                  <div key={property.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <img
                          src={property.images[0]}
                          alt={property.title}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{property.title}</h3>
                              <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                                <MapPin className="h-4 w-4" />
                                <span>{property.location.city}, {property.location.state}</span>
                                <span>•</span>
                                <span className="capitalize">{property.type}</span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <StatusIcon className="h-5 w-5" />
                              <Badge variant={getStatusColor(property.status)}>
                                {getStatusLabel(property.status)}
                              </Badge>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                            <div>
                              <div className="text-sm text-gray-600">Total Value</div>
                              <div className="font-semibold financial-data">
                                {formatCompactCurrency(property.financials.totalValue, 'INR')}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Funding Progress</div>
                              <div className="font-semibold">
                                {property.tokenomics.fundingProgress.toFixed(1)}%
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-600">Developer</div>
                              <div className="font-semibold">{property.developer.name}</div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4 mt-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-1">
                              <User className="h-4 w-4" />
                              <span>Listed {formatDate(property.createdAt)}</span>
                            </div>
                            <div>
                              Expected ROI: <span className="font-medium text-green-600">
                                {property.financials.expectedROI}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2 ml-4">
                        <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />}>
                          Review
                        </Button>
                        {property.status === 'under_review' && (
                          <>
                            <Button variant="primary" size="sm" leftIcon={<CheckCircle className="h-4 w-4" />}>
                              Approve
                            </Button>
                            <Button variant="destructive" size="sm" leftIcon={<XCircle className="h-4 w-4" />}>
                              Reject
                            </Button>
                          </>
                        )}
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {filteredProperties.length === 0 && (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(AdminPropertiesPage, ['admin'])
