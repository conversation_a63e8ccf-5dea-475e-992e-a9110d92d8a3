'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getTransactionsByUser } from '@/data/mockInvestments'
import { getCurrencyInfo, isCryptocurrency } from '@/lib/currency'
import { formatCompactCurrency, formatDateTime } from '@/lib/utils'
import { WalletBalance } from '@/types'
import {
    ArrowDownRight,
    ArrowUpRight,
    Bitcoin,
    Building2,
    Coins,
    CreditCard,
    DollarSign,
    Download,
    Eye,
    Filter,
    Minus,
    Plus,
    RefreshCw,
    Search,
    Smartphone,
    TrendingUp,
    Wallet
} from 'lucide-react'
import { useState } from 'react'

function WalletPage() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'payment_methods' | 'currencies'>('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [transactionFilter, setTransactionFilter] = useState<string>('all')

  if (!user) return null

  const transactions = getTransactionsByUser(user.id)
  const currency = user.profile.preferredCurrency

  // Mock multi-currency wallet balances
  const mockWalletBalances: WalletBalance[] = [
    { currency: 'INR', balance: 125000, lockedBalance: 25000, pendingBalance: 5000 },
    { currency: 'USD', balance: 1500, lockedBalance: 300, pendingBalance: 100 },
    { currency: 'BTC', balance: 0.025, lockedBalance: 0.005, pendingBalance: 0.001 },
    { currency: 'ETH', balance: 0.85, lockedBalance: 0.15, pendingBalance: 0.05 },
    { currency: 'USDT', balance: 2500, lockedBalance: 500, pendingBalance: 100 },
    { currency: 'EUR', balance: 800, lockedBalance: 200, pendingBalance: 50 }
  ]

  // Mock payment methods (commented out as not currently used)
  // const mockPaymentMethods: PaymentMethodInfo[] = [
  //   {
  //     id: 'pm_1',
  //     userId: user.id,
  //     type: 'bank_transfer',
  //     name: 'HDFC Bank Account',
  //     details: { bankName: 'HDFC Bank', accountNumber: '****5678' },
  //     isDefault: true,
  //     isVerified: true,
  //     createdAt: '2024-01-15T10:30:00Z',
  //     lastUsed: '2024-08-04T09:15:00Z'
  //   },
  //   {
  //     id: 'pm_2',
  //     userId: user.id,
  //     type: 'upi',
  //     name: 'UPI Payment',
  //     details: { upiId: 'user@paytm' },
  //     isDefault: false,
  //     isVerified: true,
  //     createdAt: '2024-01-20T14:20:00Z',
  //     lastUsed: '2024-08-03T16:45:00Z'
  //   },
  //   {
  //     id: 'pm_3',
  //     userId: user.id,
  //     type: 'metamask',
  //     name: 'MetaMask Wallet',
  //     details: { walletAddress: '0x742d35Cc6634C0532925a3b8D4C9db...', walletType: 'metamask', network: 'Ethereum' },
  //     isDefault: false,
  //     isVerified: true,
  //     createdAt: '2024-02-01T11:10:00Z',
  //     lastUsed: '2024-08-02T20:30:00Z'
  //   },
  //   {
  //     id: 'pm_4',
  //     userId: user.id,
  //     type: 'coinbase',
  //     name: 'Coinbase Wallet',
  //     details: { walletAddress: '**********************************', walletType: 'coinbase', network: 'Bitcoin' },
  //     isDefault: false,
  //     isVerified: true,
  //     createdAt: '2024-02-10T09:30:00Z'
  //   }
  // ]

  // Get primary balance in user's preferred currency
  const primaryBalance = mockWalletBalances.find(b => b.currency === currency) || mockWalletBalances[0]
  const walletBalance = primaryBalance.balance
  const pendingAmount = primaryBalance.pendingBalance
  const totalInvested = transactions
    .filter(t => t.type === 'investment' && t.status === 'completed')
    .reduce((sum, t) => sum + t.amount, 0)
  const totalDistributions = transactions
    .filter(t => t.type === 'distribution' && t.status === 'completed')
    .reduce((sum, t) => sum + t.amount, 0)

  // Mock additional transactions for better demo
  const mockTransactions = [
    {
      id: 'txn_001',
      type: 'deposit',
      amount: 50000,
      currency: 'INR',
      status: 'completed',
      description: 'Bank Transfer - HDFC Account',
      createdAt: '2024-01-15T10:30:00Z'
    },
    {
      id: 'txn_002',
      type: 'investment',
      amount: 25000,
      currency: 'INR',
      status: 'completed',
      description: 'Investment in GIFT City IT Hub',
      createdAt: '2024-01-14T14:20:00Z'
    },
    {
      id: 'txn_003',
      type: 'distribution',
      amount: 2150,
      currency: 'INR',
      status: 'completed',
      description: 'Monthly Distribution - Bandra Residential',
      createdAt: '2024-01-10T09:15:00Z'
    },
    {
      id: 'txn_004',
      type: 'withdrawal',
      amount: 5000,
      currency: 'INR',
      status: 'pending',
      description: 'Withdrawal to Bank Account',
      createdAt: '2024-01-08T16:45:00Z'
    },
    {
      id: 'txn_005',
      type: 'investment',
      amount: 75000,
      currency: 'INR',
      status: 'completed',
      description: 'Investment in Pune Commercial Complex',
      createdAt: '2024-01-05T11:30:00Z'
    }
  ]

  const allTransactions = [...transactions, ...mockTransactions]

  const filteredTransactions = allTransactions.filter(transaction => {
    const matchesSearch = searchQuery === '' ||
      transaction.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesFilter = transactionFilter === 'all' || transaction.type === transactionFilter

    return matchesSearch && matchesFilter
  })

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'transactions', label: 'Transactions', count: allTransactions.length },
    { id: 'payment_methods', label: 'Payment Methods' }
  ]

  const transactionTypes = [
    { value: 'all', label: 'All Transactions' },
    { value: 'investment', label: 'Investments' },
    { value: 'distribution', label: 'Distributions' },
    { value: 'withdrawal', label: 'Withdrawals' },
    { value: 'deposit', label: 'Deposits' }
  ]

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'investment': return ArrowUpRight
      case 'distribution': return ArrowDownRight
      case 'withdrawal': return Minus
      case 'deposit': return Plus
      default: return ArrowUpRight
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'investment': return 'text-red-600'
      case 'distribution': return 'text-green-600'
      case 'withdrawal': return 'text-red-600'
      case 'deposit': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success'
      case 'pending': return 'warning'
      case 'failed': return 'error'
      default: return 'default'
    }
  }

  // Button handlers
  const handleAddMoney = () => {
    alert('Add Money functionality - In a real app, this would open a payment gateway')
  }

  const handleWithdraw = () => {
    alert('Withdraw functionality - In a real app, this would open withdrawal form')
  }

  const handlePaymentMethods = () => {
    setActiveTab('payment_methods')
  }

  const handleDownloadStatement = () => {
    alert('Download Statement - In a real app, this would generate and download a PDF statement')
  }

  const handleViewTransaction = (transactionId: string) => {
    alert(`View Transaction Details for: ${transactionId}`)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Wallet</h1>
            <p className="text-gray-600 mt-1">
              Manage your funds and payment methods
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            <Button variant="outline" leftIcon={<RefreshCw className="h-4 w-4" />}>
              Refresh
            </Button>
            <Button variant="primary" leftIcon={<Plus className="h-4 w-4" />} onClick={handleAddMoney}>
              Add Money
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <Card>
          <CardContent className="p-0">
            <div className="flex border-b border-gray-200">
              {[
                { id: 'overview', label: 'Overview', icon: Wallet },
                { id: 'currencies', label: 'Currencies', icon: Coins },
                { id: 'transactions', label: 'Transactions', icon: ArrowDownRight },
                { id: 'payment_methods', label: 'Payment Methods', icon: CreditCard }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as 'overview' | 'transactions' | 'payment_methods' | 'currencies')}
                    className={`flex items-center px-6 py-4 text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {activeTab === 'overview' && (
          <>
            {/* Wallet Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Available Balance</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(walletBalance, currency)}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Wallet className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Amount</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(pendingAmount, currency)}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <RefreshCw className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Invested</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(totalInvested, currency)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <ArrowUpRight className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Received</p>
                  <p className="text-2xl font-bold text-gray-900 financial-data">
                    {formatCompactCurrency(totalDistributions, currency)}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <ArrowDownRight className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex-col" onClick={handleAddMoney}>
                <Plus className="h-6 w-6 mb-2" />
                Add Money
              </Button>
              <Button variant="outline" className="h-20 flex-col" onClick={handleWithdraw}>
                <Minus className="h-6 w-6 mb-2" />
                Withdraw
              </Button>
              <Button variant="outline" className="h-20 flex-col" onClick={handlePaymentMethods}>
                <CreditCard className="h-6 w-6 mb-2" />
                Payment Methods
              </Button>
              <Button variant="outline" className="h-20 flex-col" onClick={handleDownloadStatement}>
                <Download className="h-6 w-6 mb-2" />
                Download Statement
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 border-b border-gray-200">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'overview' | 'transactions' | 'payment_methods')}
                  className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                  {tab.count && (
                    <span className="ml-2 px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Recent Transactions */}
                <div>
                  <h3 className="font-semibold text-gray-900 mb-4">Recent Transactions</h3>
                  <div className="space-y-3">
                    {transactions.slice(0, 5).map((transaction) => {
                      const Icon = getTransactionIcon(transaction.type)
                      return (
                        <div key={transaction.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              transaction.type === 'distribution' ? 'bg-green-100' : 'bg-blue-100'
                            }`}>
                              <Icon className={`h-5 w-5 ${getTransactionColor(transaction.type)}`} />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{transaction.description}</div>
                              <div className="text-sm text-gray-500">
                                {formatDateTime(transaction.createdAt)}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`font-semibold ${getTransactionColor(transaction.type)} financial-data`}>
                              {transaction.type === 'investment' || transaction.type === 'withdrawal' ? '-' : '+'}
                              {formatCompactCurrency(transaction.amount, transaction.currency)}
                            </div>
                            <Badge variant={getStatusColor(transaction.status)} size="sm">
                              {transaction.status}
                            </Badge>
                            <div className="mt-1">
                              <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />} onClick={() => handleViewTransaction(transaction.id)}>
                                View
                              </Button>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* Monthly Summary */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">This Month&apos;s Income</h3>
                    <div className="text-2xl font-bold text-green-600 financial-data">
                      {formatCompactCurrency(2150, currency)}
                    </div>
                    <div className="text-sm text-green-600 mt-1">From distributions</div>
                  </div>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-2">This Month&apos;s Investments</h3>
                    <div className="text-2xl font-bold text-blue-600 financial-data">
                      {formatCompactCurrency(100000, currency)}
                    </div>
                    <div className="text-sm text-blue-600 mt-1">In 2 properties</div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'transactions' && (
              <div className="space-y-4">
                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search transactions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      leftIcon={<Search className="h-4 w-4" />}
                    />
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={transactionFilter}
                      onChange={(e) => setTransactionFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      {transactionTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                    <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                      More Filters
                    </Button>
                  </div>
                </div>

                {/* Transactions List */}
                <div className="space-y-3">
                  {filteredTransactions.map((transaction) => {
                    const Icon = getTransactionIcon(transaction.type)
                    return (
                      <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex items-center space-x-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                            transaction.type === 'distribution' ? 'bg-green-100' :
                            transaction.type === 'investment' ? 'bg-blue-100' : 'bg-gray-100'
                          }`}>
                            <Icon className={`h-6 w-6 ${getTransactionColor(transaction.type)}`} />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{transaction.description}</div>
                            <div className="text-sm text-gray-500">
                              {formatDateTime(transaction.createdAt)}
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              ID: {transaction.id}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-lg font-semibold ${getTransactionColor(transaction.type)} financial-data`}>
                            {transaction.type === 'investment' ? '-' : '+'}
                            {formatCompactCurrency(transaction.amount, transaction.currency)}
                          </div>
                          <Badge variant={getStatusColor(transaction.status)} size="sm">
                            {transaction.status}
                          </Badge>
                          <div className="mt-2">
                            <Button variant="outline" size="sm" leftIcon={<Eye className="h-4 w-4" />} onClick={() => handleViewTransaction(transaction.id)}>
                              View
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {filteredTransactions.length === 0 && (
                  <div className="text-center py-12">
                    <Wallet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
                    <p className="text-gray-600">
                      Try adjusting your search criteria or filters.
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'payment_methods' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Saved Payment Methods</h3>
                  <Button variant="primary" leftIcon={<Plus className="h-4 w-4" />}>
                    Add Payment Method
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <CreditCard className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">•••• •••• •••• 4532</div>
                        <div className="text-sm text-gray-500">Visa ending in 4532</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="success">Primary</Badge>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <Smartphone className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">UPI ID: user@paytm</div>
                        <div className="text-sm text-gray-500">Paytm UPI</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Building2 className="h-6 w-6 text-purple-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">HDFC Bank ••••5678</div>
                        <div className="text-sm text-gray-500">Savings Account</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        </>
        )}

        {activeTab === 'currencies' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Coins className="h-5 w-5 mr-2" />
                Multi-Currency Wallet
              </CardTitle>
              <p className="text-gray-600">Manage your fiat and cryptocurrency balances</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Currency Selector */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Your Balances</h3>
                  <Button variant="outline" leftIcon={<Plus className="h-4 w-4" />}>
                    Add Currency
                  </Button>
                </div>

                {/* Currency Balances Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {mockWalletBalances.map((balance) => {
                    const currencyInfo = getCurrencyInfo(balance.currency)
                    const isCrypto = isCryptocurrency(balance.currency)

                    return (
                      <div key={balance.currency} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              isCrypto ? 'bg-orange-100' : 'bg-blue-100'
                            }`}>
                              {isCrypto ? (
                                <Bitcoin className={`h-5 w-5 ${isCrypto ? 'text-orange-600' : 'text-blue-600'}`} />
                              ) : (
                                <DollarSign className="h-5 w-5 text-blue-600" />
                              )}
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">{balance.currency}</div>
                              <div className="text-sm text-gray-500">{currencyInfo?.name}</div>
                            </div>
                          </div>
                          <Badge variant={balance.balance > 0 ? 'success' : 'default'}>
                            {isCrypto ? 'Crypto' : 'Fiat'}
                          </Badge>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Available:</span>
                            <span className="font-medium">{formatCompactCurrency(balance.balance, balance.currency)}</span>
                          </div>
                          {balance.lockedBalance > 0 && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Locked:</span>
                              <span className="text-sm text-orange-600">{formatCompactCurrency(balance.lockedBalance, balance.currency)}</span>
                            </div>
                          )}
                          {balance.pendingBalance > 0 && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Pending:</span>
                              <span className="text-sm text-yellow-600">{formatCompactCurrency(balance.pendingBalance, balance.currency)}</span>
                            </div>
                          )}
                        </div>

                        <div className="mt-4 flex space-x-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <Plus className="h-3 w-3 mr-1" />
                            Add
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <Minus className="h-3 w-3 mr-1" />
                            Send
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Trade
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Add New Currency */}
                <div className="mt-6 p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
                  <Coins className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <h3 className="text-lg font-medium text-gray-900 mb-1">Add More Currencies</h3>
                  <p className="text-gray-600 mb-4">Support for 50+ fiat currencies and major cryptocurrencies</p>
                  <Button variant="primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Browse Currencies
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}

export default withAuth(WalletPage, ['retail_investor', 'institutional_investor'])
