'use client'

import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { Input } from '@/components/ui/Input'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { getInvestmentsByUser, mockSecondaryOrders } from '@/data/mockInvestments'
import { getMarketDataByProperty } from '@/data/mockMarketData'
import { mockProperties } from '@/data/mockProperties'
import { formatCompactCurrency, formatDateTime, formatPercentage } from '@/lib/utils'
import {
    ArrowDownRight,
    ArrowUpRight,
    BarChart3,
    Building2,
    Clock,
    Filter,
    Minus,
    Plus,
    Search,
    TrendingUp,
    Users
} from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

function SecondaryMarketPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'market' | 'my_orders' | 'my_holdings'>('market')

  const [searchQuery, setSearchQuery] = useState('')

  if (!user) return null

  const userInvestments = getInvestmentsByUser(user.id)
  const userOrders = mockSecondaryOrders.filter(order => order.userId === user.id)

  // Trade handlers
  const handleBuyOrder = (propertyId: string, price: number) => {
    alert(`Create Buy Order for Property: ${propertyId} at ₹${price.toLocaleString()} per token`)
  }

  const handleSellOrder = (propertyId: string, price: number) => {
    alert(`Create Sell Order for Property: ${propertyId} at ₹${price.toLocaleString()} per token`)
  }


  
  // Get properties with market data
  const propertiesWithMarket = mockProperties
    .filter(p => p.status === 'active_funding' || p.status === 'fully_funded')
    .map(property => {
      const marketData = getMarketDataByProperty(property.id)
      return { property, marketData }
    })
    .filter(item => item.marketData)

  const filteredProperties = propertiesWithMarket.filter(({ property }) =>
    searchQuery === '' || 
    property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    property.location.city.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const tabs = [
    { id: 'market', label: 'Market', count: filteredProperties.length },
    { id: 'my_orders', label: 'My Orders', count: userOrders.length },
    { id: 'my_holdings', label: 'My Holdings', count: userInvestments.length }
  ]

  const getPropertyById = (id: string) => {
    return mockProperties.find(p => p.id === id)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Secondary Market</h1>
            <p className="text-gray-600 mt-1">
              Trade property tokens with other investors
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary" leftIcon={<Plus className="h-4 w-4" />}>
              Create Order
            </Button>
          </div>
        </div>

        {/* Market Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">47</div>
              <div className="text-sm text-gray-600">Active Properties</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">₹12.5Cr</div>
              <div className="text-sm text-gray-600">24h Volume</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">1,247</div>
              <div className="text-sm text-gray-600">Active Traders</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">156</div>
              <div className="text-sm text-gray-600">Open Orders</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search properties or locations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
              <Button variant="outline" leftIcon={<Filter className="h-4 w-4" />}>
                Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 border-b border-gray-200">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'market' | 'my_orders' | 'my_holdings')}
                  className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                  {tab.count > 0 && (
                    <span className="ml-2 px-2 py-0.5 text-xs bg-gray-100 rounded-full">
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {activeTab === 'market' && (
              <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  Showing {filteredProperties.length} properties available for trading
                </div>
                
                <div className="space-y-4">
                  {filteredProperties.map(({ property, marketData }) => (
                    <div key={property.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="w-16 h-16 overflow-hidden rounded-lg">
                            <Image
                              src={property.images[0]}
                              alt={property.title}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNkI3MjgwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Qcm9wZXJ0eTwvdGV4dD48L3N2Zz4=`;
                              }}
                            />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">{property.title}</h3>
                            <p className="text-sm text-gray-600 mb-2">
                              {property.location.city}, {property.location.country}
                            </p>
                            <div className="flex items-center space-x-4 text-sm">
                              <div>
                                <span className="text-gray-600">Current Price: </span>
                                <span className="font-semibold financial-data">
                                  {formatCompactCurrency(marketData!.currentPrice, property.financials.currency)}
                                </span>
                              </div>
                              <div className={`flex items-center ${
                                marketData!.priceChangePercent24h >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {marketData!.priceChangePercent24h >= 0 ? (
                                  <ArrowUpRight className="h-4 w-4 mr-1" />
                                ) : (
                                  <ArrowDownRight className="h-4 w-4 mr-1" />
                                )}
                                <span className="font-semibold">
                                  {marketData!.priceChangePercent24h >= 0 ? '+' : ''}
                                  {formatPercentage(marketData!.priceChangePercent24h)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/properties/${property.id}`)}
                          >
                            View Details
                          </Button>
                          <Button
                            variant="primary"
                            size="sm"
                            leftIcon={<Plus className="h-4 w-4" />}
                            onClick={() => handleBuyOrder(property.id, marketData!.currentPrice)}
                          >
                            Buy
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            leftIcon={<Minus className="h-4 w-4" />}
                            onClick={() => handleSellOrder(property.id, marketData!.currentPrice)}
                          >
                            Sell
                          </Button>
                        </div>
                      </div>
                      
                      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">24h Volume</div>
                          <div className="font-semibold">{marketData!.volume24h} tokens</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Market Cap</div>
                          <div className="font-semibold financial-data">
                            {formatCompactCurrency(marketData!.marketCap, property.financials.currency)}
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">Expected ROI</div>
                          <div className="font-semibold text-green-600">
                            {formatPercentage(property.financials.expectedROI)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'my_orders' && (
              <div className="space-y-4">
                {userOrders.length === 0 ? (
                  <div className="text-center py-12">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Orders</h3>
                    <p className="text-gray-600 mb-4">You don&apos;t have any active buy or sell orders.</p>
                    <Button variant="primary">Create Your First Order</Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {userOrders.map((order) => {
                      const property = getPropertyById(order.propertyId)
                      return (
                        <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Badge variant={order.type === 'buy' ? 'success' : 'error'}>
                                {order.type === 'buy' ? 'BUY' : 'SELL'}
                              </Badge>
                              <div>
                                <div className="font-medium text-gray-900">{property?.title}</div>
                                <div className="text-sm text-gray-500">
                                  {order.tokens} tokens @ {formatCompactCurrency(order.pricePerToken, 'INR')}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold financial-data">
                                {formatCompactCurrency(order.totalAmount, 'INR')}
                              </div>
                              <div className="text-sm text-gray-500">
                                {formatDateTime(order.createdAt)}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'my_holdings' && (
              <div className="space-y-4">
                {userInvestments.length === 0 ? (
                  <div className="text-center py-12">
                    <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Holdings</h3>
                    <p className="text-gray-600 mb-4">You don&apos;t own any property tokens yet.</p>
                    <Button variant="primary" onClick={() => router.push('/properties')}>
                      Browse Properties
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {userInvestments.map((investment) => {
                      const property = getPropertyById(investment.propertyId)
                      const marketData = getMarketDataByProperty(investment.propertyId)
                      const currentValue = marketData ? investment.tokens * marketData.currentPrice : investment.currentValue
                      
                      return (
                        <div key={investment.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4">
                              <div className="w-12 h-12 overflow-hidden rounded-lg">
                                <Image
                                  src={property?.images[0] || ''}
                                  alt={property?.title || ''}
                                  width={48}
                                  height={48}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{property?.title}</div>
                                <div className="text-sm text-gray-500 mb-2">
                                  {property?.location.city}, {property?.location.country}
                                </div>
                                <div className="flex items-center space-x-4 text-sm">
                                  <div>
                                    <span className="text-gray-600">Tokens: </span>
                                    <span className="font-semibold">{investment.tokens}</span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">Avg Price: </span>
                                    <span className="font-semibold financial-data">
                                      {formatCompactCurrency(investment.tokenPrice, 'INR')}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="text-right">
                              <div className="font-semibold financial-data">
                                {formatCompactCurrency(currentValue, 'INR')}
                              </div>
                              <div className={`text-sm font-medium ${
                                investment.roi >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {investment.roi >= 0 ? '+' : ''}{formatPercentage(investment.roi)}
                              </div>
                              <div className="mt-2 space-x-2">
                                <Button variant="outline" size="sm">
                                  <Minus className="h-3 w-3 mr-1" />
                                  Sell
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withAuth(SecondaryMarketPage, ['retail_investor', 'institutional_investor'])
