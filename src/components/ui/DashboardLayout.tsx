'use client'

import { useAuth } from '@/contexts/AuthContext'
import { cn } from '@/lib/utils'
import {
    BarChart3,
    Bell,
    Building2,
    FileText,
    Home,
    LogOut,
    Menu,
    PlusCircle,
    Settings,
    Shield,
    TrendingUp,
    User,
    Users,
    Wallet,
    X
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { Badge } from './Badge'
import { Button } from './Button'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, logout } = useAuth()
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const getNavigationItems = () => {
    const baseItems = [
      { name: 'Dashboard', href: '/dashboard', icon: Home },
      { name: 'Properties', href: '/properties', icon: Building2 },
    ]

    switch (user?.userType) {
      case 'retail_investor':
      case 'institutional_investor':
        return [
          ...baseItems,
          { name: 'Portfolio', href: '/portfolio', icon: TrendingUp },
          { name: 'Wallet', href: '/wallet', icon: Wallet },
          { name: 'Secondary Market', href: '/secondary-market', icon: BarChart3 },
          { name: 'Settings', href: '/settings', icon: Settings },
        ]
      
      case 'developer':
        return [
          ...baseItems,
          { name: 'My Properties', href: '/developer/properties', icon: Building2 },
          { name: 'Add Property', href: '/developer/add-property', icon: PlusCircle },
          { name: 'Analytics', href: '/developer/analytics', icon: BarChart3 },
          { name: 'Investors', href: '/developer/investors', icon: Users },
          { name: 'Settings', href: '/settings', icon: Settings },
        ]
      
      case 'admin':
        return [
          ...baseItems,
          { name: 'User Management', href: '/admin/users', icon: Users },
          { name: 'Property Oversight', href: '/admin/properties', icon: Building2 },
          { name: 'KYC Review', href: '/admin/kyc', icon: Shield },
          { name: 'Reports', href: '/admin/reports', icon: FileText },
          { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
          { name: 'Settings', href: '/settings', icon: Settings },
        ]
      
      default:
        return baseItems
    }
  }

  const navigationItems = getNavigationItems()

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'retail_investor': return 'Retail Investor'
      case 'institutional_investor': return 'Institutional Investor'
      case 'developer': return 'Developer'
      case 'admin': return 'Admin'
      default: return 'User'
    }
  }

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'retail_investor': return 'info'
      case 'institutional_investor': return 'secondary'
      case 'developer': return 'success'
      case 'admin': return 'warning'
      default: return 'default'
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      )}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <div className="flex items-center">
              <Image
                src="/images/logos/realflow.png"
                alt="RealFlow Logo"
                width={120}
                height={32}
                className="h-8 w-auto"
              />
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user?.name}
                </p>
                <Badge variant={getUserTypeColor(user?.userType || '')} size="sm">
                  {getUserTypeLabel(user?.userType || '')}
                </Badge>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            {navigationItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.name}
                  onClick={() => {
                    router.push(item.href)
                    setSidebarOpen(false)
                  }}
                  className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors"
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </button>
              )
            })}
          </nav>

          {/* Logout */}
          <div className="p-4 border-t border-gray-200">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="w-full justify-start"
              leftIcon={<LogOut className="h-4 w-4" />}
            >
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md hover:bg-gray-100"
            >
              <Menu className="h-5 w-5" />
            </button>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="p-2 rounded-full hover:bg-gray-100 relative">
                <Bell className="h-5 w-5 text-gray-600" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* User menu */}
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
