'use client'

import { useAuth } from '@/contexts/AuthContext'
import { formatCompactCurrency, formatPercentage } from '@/lib/utils'
import { Property } from '@/types'
import { ArrowRight, Calendar, MapPin, TrendingUp, Users } from 'lucide-react'
import Image from 'next/image'

import { Badge } from './Badge'
import { Button } from './Button'
import { Card, CardContent, CardFooter } from './Card'

interface PropertyCardProps {
  property: Property
  onViewDetails?: (propertyId: string) => void
  onInvest?: (propertyId: string) => void
  showInvestButton?: boolean
  variant?: 'default' | 'compact' | 'detailed'
}

export function PropertyCard({ 
  property, 
  onViewDetails, 
  onInvest, 
  showInvestButton = true,
  variant = 'default' 
}: PropertyCardProps) {
  const { user, isAuthenticated } = useAuth()
  
  const canInvest = isAuthenticated && 
    user?.kycStatus === 'verified' && 
    property.status === 'active_funding' &&
    property.tokenomics.availableTokens > 0

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active_funding': return 'info'
      case 'fully_funded': return 'success'
      case 'completed': return 'success'
      case 'under_review': return 'warning'
      default: return 'default'
    }
  }

  const formatStatus = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  if (variant === 'compact') {
    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => onViewDetails?.(property.id)}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm text-gray-900 truncate">{property.title}</h3>
              <p className="text-xs text-gray-500 flex items-center mt-1">
                <MapPin className="h-3 w-3 mr-1" />
                {property.location.city}, {property.location.country}
              </p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm font-medium text-gray-900">
                  {formatCompactCurrency(property.tokenomics.tokenPrice, property.financials.currency)}
                </span>
                <Badge variant={getStatusVariant(property.status)} size="sm">
                  {formatStatus(property.status)}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="hover:shadow-lg transition-all duration-300 overflow-hidden">
      {/* Property Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={property.images[0]}
          alt={property.title}
          fill
          className="object-cover transition-transform duration-300 hover:scale-105"
        />
        <div className="hidden absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600"></div>
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="absolute top-4 left-4">
          <Badge variant={getStatusVariant(property.status)}>
            {formatStatus(property.status)}
          </Badge>
        </div>
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-white bg-opacity-90 text-gray-800">
            {property.type === 'residential' ? 'Residential' : 'Commercial'}
          </Badge>
        </div>
        <div className="absolute bottom-4 left-4 text-white">
          <h3 className="text-lg font-semibold mb-1 drop-shadow-lg">{property.title}</h3>
          <p className="text-sm opacity-90 flex items-center drop-shadow-lg">
            <MapPin className="h-4 w-4 mr-1" />
            {property.location.city}, {property.location.country}
          </p>
        </div>
      </div>

      <CardContent>
        <div className="space-y-4">
          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center mb-1">
                <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                <span className="text-sm font-medium text-gray-600">Expected ROI</span>
              </div>
              <div className="text-lg font-bold text-green-600">
                {formatPercentage(property.financials.expectedROI)}
              </div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center mb-1">
                <Users className="h-4 w-4 text-blue-600 mr-1" />
                <span className="text-sm font-medium text-gray-600">Funded</span>
              </div>
              <div className="text-lg font-bold text-blue-600">
                {formatPercentage(property.tokenomics.fundingProgress)}
              </div>
            </div>
          </div>

          {/* Investment Details */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Token Price</span>
              <span className="font-semibold financial-data">
                {formatCompactCurrency(property.tokenomics.tokenPrice, property.financials.currency)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Min Investment</span>
              <span className="font-semibold financial-data">
                {formatCompactCurrency(property.tokenomics.minimumInvestment, property.financials.currency)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Available Tokens</span>
              <span className="font-semibold text-gray-900">
                {property.tokenomics.availableTokens.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Funding Progress</span>
              <span className="font-medium">{formatPercentage(property.tokenomics.fundingProgress)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(property.tokenomics.fundingProgress, 100)}%` }}
              ></div>
            </div>
          </div>

          {/* Developer Info */}
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-semibold">
                {property.developer.name.charAt(0)}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {property.developer.name}
              </p>
              <p className="text-xs text-gray-500">
                {property.developer.successRate}% Success Rate
              </p>
            </div>
          </div>

          {/* Timeline */}
          {property.timeline.fundingEndDate && (
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              <span>
                Funding ends: {new Date(property.timeline.fundingEndDate).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="space-x-2">
        <Button
          variant="outline"
          size="sm"
          className="flex-1"
          onClick={() => onViewDetails?.(property.id)}
          rightIcon={<ArrowRight className="h-4 w-4" />}
        >
          View Details
        </Button>
        {showInvestButton && (
          <Button
            variant="primary"
            size="sm"
            className="flex-1"
            onClick={() => onInvest?.(property.id)}
            disabled={!canInvest}
          >
            {canInvest ? 'Invest Now' : 
             !isAuthenticated ? 'Login to Invest' :
             user?.kycStatus !== 'verified' ? 'Complete KYC' :
             property.status !== 'active_funding' ? 'Not Available' :
             'Fully Funded'}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
