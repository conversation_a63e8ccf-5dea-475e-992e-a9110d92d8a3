/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for S3 deployment
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Completely disable image optimization for static export
  images: {
    unoptimized: true,
    disableStaticImages: true
  },

  // Note: rewrites don't work with static export, so we remove them

  // Disable ESLint during build for prototype
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable TypeScript errors during build for prototype
  typescript: {
    ignoreBuildErrors: true,
  },

  // Note: Dynamic routes have been temporarily disabled for static export
  // They are moved to _id_disabled folders
}

module.exports = nextConfig
