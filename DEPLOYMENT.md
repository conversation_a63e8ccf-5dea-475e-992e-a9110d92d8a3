# RealFlow - Static Deployment Guide

## AWS S3 Static Website Deployment

This Next.js application has been configured for static export and can be deployed to AWS S3 as a static website.

### Build Process

The application is configured with:
- **Static Export**: `output: 'export'` in `next.config.js`
- **Image Optimization**: Disabled for static hosting
- **ESLint**: Disabled for prototype development
- **TypeScript**: Build errors ignored for prototype

### Build Commands

```bash
# Build for static export
npm run export

# Alternative (same as export)
npm run build
```

### Generated Output

The build generates static files in the `out/` directory:
- `out/index.html` - Home page
- `out/dashboard/index.html` - Dashboard
- `out/login/index.html` - Login page
- `out/admin/*/index.html` - Admin pages
- `out/developer/*/index.html` - Developer pages
- `out/_next/static/` - Static assets (CSS, JS, images)

### AWS S3 Deployment Steps

1. **Create S3 Bucket**
   ```bash
   aws s3 mb s3://your-realflow-bucket-name
   ```

2. **Upload Static Files**
   ```bash
   aws s3 sync out/ s3://your-realflow-bucket-name --delete
   ```

3. **Configure Static Website Hosting**
   ```bash
   aws s3 website s3://your-realflow-bucket-name \
     --index-document index.html \
     --error-document 404.html
   ```

4. **Set Bucket Policy for Public Access**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::your-realflow-bucket-name/*"
       }
     ]
   }
   ```

### Alternative Deployment Options

- **Netlify**: Drag and drop the `out/` folder
- **Vercel**: Use `vercel --prod` (though static export is not needed for Vercel)
- **GitHub Pages**: Push `out/` contents to `gh-pages` branch
- **Any Static Host**: Upload `out/` directory contents

### Important Notes

- **Dynamic Routes**: Temporarily disabled for static export
  - `src/app/properties/[id]` → moved to `_id_disabled`
  - `src/app/developer/properties/[id]` → moved to `_id_disabled`
- **Client-Side Routing**: Works within the app
- **API Routes**: Not supported in static export
- **Authentication**: Uses mock data for prototype

### Re-enabling Dynamic Routes

To re-enable dynamic routes for development:
1. Move `_id_disabled` folders back to `[id]`
2. Remove `output: 'export'` from `next.config.js`
3. Add `generateStaticParams()` functions for static generation

### Environment Variables

No environment variables are required for the static build as this is a prototype with mock data.

### Performance

- **Bundle Size**: ~100KB first load JS
- **Static Assets**: Optimized and compressed
- **Images**: Unoptimized (suitable for prototype)
- **Caching**: Handled by S3/CDN configuration
